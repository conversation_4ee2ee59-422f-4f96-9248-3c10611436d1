// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/dao/internal/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdBatchTaskDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdBatchTaskDetailDao struct {
	table   string                   // Table is the underlying table name of the DAO.
	group   string                   // Group is the database configuration group name of current DAO.
	columns AdBatchTaskDetailColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdBatchTaskDetailColumns defines and stores column names for table ad_batch_task_detail.
type AdBatchTaskDetailColumns struct {
	Id                       string //
	TaskId                   string // 任务ID
	SerialNumber             string // 序号 任务中的执行顺序
	AdvertiserId             string // 媒体账户ID
	ProjectId                string // 项目ID
	PromotionId              string // 广告ID
	CampaignId               string // 广告计划ID
	UnitId                   string // 广告组ID
	OriginalUnitName         string // 原广告组名称
	NewUnitName              string // 新广告组名称
	OriginalAdvertiserName   string // 原账户名称
	NewAdvertiserName        string // 新账户名称
	OriginalAdvertiserRemark string // 原账户备注
	NewAdvertiserRemark      string // 新账户备注
	OptResult                string // 执行结果：SUCCESS：成功  FAIL：失败
	ErrMsg                   string // 失败原因
	CreatedAt                string // 创建时间
	UpdatedAt                string // 更新时间
	DeletedAt                string // 删除时间
}

var adBatchTaskDetailColumns = AdBatchTaskDetailColumns{
	Id:                       "id",
	TaskId:                   "task_id",
	SerialNumber:             "serial_number",
	AdvertiserId:             "advertiser_id",
	ProjectId:                "project_id",
	PromotionId:              "promotion_id",
	CampaignId:               "campaign_id",
	UnitId:                   "unit_id",
	OriginalUnitName:         "original_unit_name",
	NewUnitName:              "new_unit_name",
	OriginalAdvertiserName:   "original_advertiser_name",
	NewAdvertiserName:        "new_advertiser_name",
	OriginalAdvertiserRemark: "original_advertiser_remark",
	NewAdvertiserRemark:      "new_advertiser_remark",
	OptResult:                "opt_result",
	ErrMsg:                   "err_msg",
	CreatedAt:                "created_at",
	UpdatedAt:                "updated_at",
	DeletedAt:                "deleted_at",
}

// NewAdBatchTaskDetailDao creates and returns a new DAO object for table data access.
func NewAdBatchTaskDetailDao() *AdBatchTaskDetailDao {
	return &AdBatchTaskDetailDao{
		group:   "default",
		table:   "ad_batch_task_detail",
		columns: adBatchTaskDetailColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdBatchTaskDetailDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdBatchTaskDetailDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdBatchTaskDetailDao) Columns() AdBatchTaskDetailColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdBatchTaskDetailDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdBatchTaskDetailDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdBatchTaskDetailDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
