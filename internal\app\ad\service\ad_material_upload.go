// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-12-23 15:51:09
// 生成路径: internal/app/ad/service/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdMaterialUpload interface {
	List(ctx context.Context, req *model.AdMaterialUploadSearchReq) (res *model.AdMaterialUploadSearchRes, err error)
	GetById(ctx context.Context, Id int) (res *model.AdMaterialUploadInfoRes, err error)
	GetByIds(ctx context.Context, req []int) (res []*model.AdMaterialUploadInfoRes, err error)
	GetByTaskId(ctx context.Context, req string) (res []*model.AdMaterialUploadInfoRes, err error)
	Add(ctx context.Context, req *model.AdMaterialUploadAddReq) (err error)
	Edit(ctx context.Context, req *model.AdMaterialUploadEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
	Pull(ctx context.Context, id string, materialType string) error
	UpLoad(ctx context.Context, aId string, fileId int) (meadId string, err error)
	IsExist(ctx context.Context, aId string, fileId int) (isExist bool, mediaId string, err error)
	UpLoadVideoAsync(ctx context.Context, aId string, fileId int) (taskId int64, err error)
	GetUpLoadVideoAsync(ctx context.Context, aId string, taskId int64, file *model.AdMaterialInfoRes) (videoId string, err error)
	//UpLoadVideos(ctx context.Context, aId string, fileId []int) (meadIdMap map[int]string, err error)
	UpLoadByUrl(ctx context.Context, aId string, url string, fileName string) (meadId string, err error)
	SyncMediaMaterialToLocal(ctx context.Context, req *model.SyncMediaMaterialToLocalReq) error
}

var localAdMaterialUpload IAdMaterialUpload

func AdMaterialUpload() IAdMaterialUpload {
	if localAdMaterialUpload == nil {
		panic("implement not found for interface IAdMaterialUpload, forgot register?")
	}
	return localAdMaterialUpload
}

func RegisterAdMaterialUpload(i IAdMaterialUpload) {
	localAdMaterialUpload = i
}
