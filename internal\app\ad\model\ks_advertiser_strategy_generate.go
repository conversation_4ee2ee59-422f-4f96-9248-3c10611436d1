// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package model

// KsAdvertiserStrategyGenerateReq 快手广告策略生成请求参数
type KsAdvertiserStrategyGenerateReq struct {
	KsAdvertiserStrategyRuleReq        *KsAdvertiserStrategyConfigAddReq   `p:"ruleConfig"  dc:"规则配置"`
	KsAdvertiserStrategyCampaignConfig *KsAdvertiserStrategyCampaignAddReq `p:"campaignConfig"  dc:"广告计划"`
	KsAdvertiserStrategyUnitConfig     *KsAdvertiserStrategyUnitAddReq     `p:"adUnitConfig"  dc:"广告组"`
	KsAdvertiserStrategyCreativeConfig *KsAdvertiserStrategyCreativeAddReq `p:"creativeConfig"  dc:"创意"`
	KsAdvertiserStrategyMaterialConfig *KsAdvertiserStrategyMaterialAddReq `p:"materialConfig"  dc:"素材"`
	KsAdvertiserStrategyTitleConfig    *KsAdvertiserStrategyTitleAddReq    `p:"titleConfig"  dc:"文案"`
}

// KsAdvertiserStrategyGenerateRes 快手广告策略生成返回结果
type KsAdvertiserStrategyGenerateRes struct {
	EstimateInfoMap map[int64]*EstimateInfo `json:"estimateInfoMap" dc:"预估信息"`
	AdvertiserList  []*AdvertiserInfo       `json:"advertiserList" dc:"广告主列表"`
	EstimateUnitNum int                     `json:"estimateUnitNum" dc:"预估广告数量"`
}

type AdvertiserInfo struct {
	AdvertiserId     string          `json:"advertiserId" dc:"广告主ID"`
	AdvertiserName   string          `json:"advertiserName" dc:"广告主名称"`
	AdvertiserRemark string          `json:"advertiserRemark" dc:"广告主备注"`
	CampaignNum      int             `json:"campaignNum" dc:"广告计划数量"`
	UnitNum          int             `json:"unitCount" dc:"广告组数量"`
	CampaignList     []*CampaignInfo `json:"campaignList" dc:"广告计划列表"`
}

type CampaignInfo struct {
	CampaignId string `json:"campaignId" dc:"广告计划ID"`
	Exist      bool   `json:"exist" dc:"广告计划是否存在"`
	*KsAdvertiserStrategyCampaignAddReq
	UnitList []*UnitInfo `json:"unitList" dc:"广告组列表"`
}

type UnitInfo struct {
	*KsAdvertiserStrategyUnitAddReq
	UnitId                   string          `json:"unitId" dc:"广告组ID"`
	ShortPlayName            string          `json:"ShortPlayName" dc:"推广短剧"`
	SeriesPayMode            int             `json:"seriesPayMode"  dc:"付费模式 1-打包，2-虚拟币，3-观看广告解锁"`
	SeriesPayModeName        string          `json:"seriesPayModeName"`
	SeriesPayTemplateId      int64           `json:"seriesPayTemplateId"  dc:"付费模板ID"`
	SeriesPayTemplateIdName  string          `json:"seriesPayTemplateIdName"`
	SeriesPayTemplateIdMulti []int64         `json:"seriesPayTemplateIdMulti" dc:"付费模版ID列表"`
	Bid                      int64           `json:"bid" dc:"出价"`
	RoiRatio                 float64         `json:"roiRatio" dc:"付费ROI系数"`
	CreativeList             []*CreativeInfo `json:"creativeList" dc:"创意列表"`
}

type CreativeInfo struct {
	TaskId            string                                 `p:"-"  dc:"任务ID"`
	CreativeConfig    *KsAdvertiserStrategyCreativeAddReq    `json:"creativeConfig" dc:"创意信息"`
	CreativeMaterials *CreativeMaterials                     `json:"creativeMaterials" dc:"创意素材"`
	Titles            []*KsAdvertiserCommonAssetTitleInfoRes `json:"titles" dc:"标题列表"`
	ProductId         uint64                                 `json:"productId" dc:"商品ID"`
	ProductName       string                                 `json:"productName" dc:"商品名称"`
}

type EstimateInfo struct {
	AdvertiserId         int64                                  `json:"advertiserId" dc:"广告主ID"`
	AdvertiserName       string                                 `json:"advertiserName" dc:"广告主名称"`
	AdvertiserRemark     string                                 `json:"advertiserRemark" dc:"广告主备注"`
	CampaignNum          int                                    `json:"campaignNum" dc:"广告计划数量"`
	UnitNum              int                                    `json:"unitNum" dc:"广告组数量"`
	CreativeMaterialList []*CreativeMaterials                   `json:"-" dc:"创意素材"`
	TitleList            []*KsAdvertiserCommonAssetTitleInfoRes `json:"-" dc:"标题列表"`
}

type PreviewBaseInfo struct {
	AdvertiserId   int64                      `json:"advertiserId" dc:"广告主ID"`
	AdvertiserName string                     `json:"advertiserName" dc:"广告主名称"`
	CampaignList   []*PreviewCampaignBaseInfo `json:"campaignList" dc:"广告计划列表"`
}

type PreviewCampaignBaseInfo struct {
	CampaignId string                 `json:"campaignId" dc:"广告计划ID"`
	UnitList   []*PreviewUnitBaseInfo `json:"unitList" dc:"广告组列表"`
}

type PreviewUnitBaseInfo struct {
	CreativeMaterials *CreativeMaterials                     `json:"creativeMaterials"  dc:"创意素材"`
	Titles            []*KsAdvertiserCommonAssetTitleInfoRes `json:"titles" dc:"标题列表"`
}

// QuerySeriesListReq 查询授权短剧列表请求参数
type QuerySeriesListReq struct {
	AdvertiserId int64  `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	UserId       int64  `json:"user_id" v:"required#快手号ID必须" dc:"快手号ID"`
	SeriesTitle  string `json:"series_title" dc:"短剧标题"`
}

// QuerySeriesEpisodeListReq 查询短剧剧集列表请求参数
type QuerySeriesEpisodeListReq struct {
	AdvertiserId int64  `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	UserId       int64  `json:"user_id" v:"required#快手号ID必须" dc:"快手号ID"`
	SeriesId     int64  `json:"series_id" v:"required#短剧ID必须" dc:"短剧ID"`
	EpisodeName  string `json:"episode_name" dc:"剧集名称"`
}

// QuerySeriesPayModeTypeReq 查询短剧付费模式请求参数
type QuerySeriesPayModeTypeReq struct {
	AdvertiserId int64 `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	UserId       int64 `json:"user_id" v:"required#快手号ID必须" dc:"快手号ID"`
	SeriesId     int64 `json:"series_id" v:"required#短剧ID必须" dc:"短剧ID"`
}

// QuerySeriesPayModeTemplateReq 查询短剧付费模板请求参数
type QuerySeriesPayModeTemplateReq struct {
	AdvertiserId        int64 `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	UserId              int64 `json:"user_id" v:"required#快手号ID必须" dc:"快手号ID"`
	SeriesId            int64 `json:"series_id" v:"required#短剧ID必须" dc:"短剧ID"`
	SeriesPayMode       int   `json:"series_pay_mode" v:"required#付费模式类型必须" dc:"付费模式类型(目前多付费模板只支持'打包'，即payMode=1)"`
	SeriesPayTemplateId int64 `json:"series_pay_template_id" dc:"付费模版id (非必填，填写则代表查询该模板id对应信息）"`
}

// QueryProductListReq 查询商品列表请求参数
type QueryProductListReq struct {
	AdvertiserId int64  `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	Name         string `json:"name" dc:"商品名称"`
	LibraryId    uint64 `json:"library_id" v:"required#商品库ID必须" dc:"商品库ID"`
	PageSize     int    `json:"page_size" v:"min:1|max:100#页大小最小1最大100" dc:"页大小"`
	CurrentPage  int    `json:"current_page" v:"min:1#当前页码最小1" dc:"当前页码"`
}

// QueryProductLibraryListReq 查询商品库列表请求参数
type QueryProductLibraryListReq struct {
	AdvertiserId int64  `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	Name         string `json:"name" dc:"商品库名称"`
	LibraryId    uint64 `json:"library_id" dc:"商品库ID"`
	Status       int    `json:"status" dc:"商品库状态 1-审核中, 2-使用中, 3-审核失败, 4-暂停使用, 5-XML初始化中, 6-XML初始化失败"`
	QueryType    int    `json:"query_type" v:"required#商品库权限类型必须" dc:"商品库权限类型 1-使用权限, 2-编辑权限(含使用权限)"`
	PageSize     int    `json:"page_size" v:"min:1|max:100#页大小最小1最大100" dc:"页大小"`
	CurrentPage  int    `json:"current_page" v:"min:1#当前页码最小1" dc:"当前页码"`
}

// QueryCreativeActionBarTextReq 查询行动号召按钮请求参数
type QueryCreativeActionBarTextReq struct {
	AdvertiserId int64 `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	CampaignType int   `json:"campaign_type" v:"required#计划类型必须" dc:"计划类型 2-提升应用安装 3-获取电商下单 4-推广品牌活动 5-收集销售线索 13-小店商品推广 14-直播推广"`
}

// QueryToolExposeTagsReq 查询创意推荐理由请求参数
type QueryToolExposeTagsReq struct {
	AdvertiserId int64 `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
	CampaignType int   `json:"campaign_type" v:"required#计划类型必须" dc:"计划类型 2-提升应用安装 3-获取电商下单 4-推广品牌活动 5-收集销售线索 13-小店商品推广 14-直播推广"`
}

type QueryCreativeCategoryReq struct {
	AdvertiserId int64 `json:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
}

type QueryCreativeCategoryRes struct {
	Label    string                     `json:"label" dc:"标签"`
	Value    int                        `json:"value" dc:"值"`
	Level    string                     `json:"level" dc:"层级"`
	Children []QueryCreativeCategoryRes `json:"children" dc:"子级"`
}

type QueryRoiRatioRes struct {
	RoiRatio float64 `json:"roiRatio" dc:"roi系数"`
}
