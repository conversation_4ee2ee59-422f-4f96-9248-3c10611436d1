// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/model/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserCommonAssetTitleInfoRes is the golang structure for table ks_advertiser_common_asset_title.
type KsAdvertiserCommonAssetTitleInfoRes struct {
	gmeta.Meta         `orm:"table:ks_advertiser_common_asset_title"`
	Id                 int         `orm:"id,primary" json:"id" dc:""`                                    //
	Title              string      `orm:"title" json:"title" dc:"标题"`                                    // 标题
	CategoryId         int         `orm:"category_id" json:"categoryId" dc:"标题分类ID"`                     // 标题分类ID
	UserId             int         `orm:"user_id" json:"userId" dc:"创建者"`                                // 创建者
	Last3DayCost       float64     `orm:"last_3_day_cost" json:"last3DayCost" dc:"近3日消耗"`                // 近3日消耗
	Last30DayCost      float64     `orm:"last_30_day_cost" json:"last30DayCost" dc:"近30日消耗"`             // 近30日消耗
	Last3DayClickRate  float64     `orm:"last_3_day_click_rate" json:"last3DayClickRate" dc:"近3日点击率"`    // 近3日点击率
	Last30DayClickRate float64     `orm:"last_30_day_click_rate" json:"last30DayClickRate" dc:"近30日点击率"` // 近30日点击率
	UnitCount          int         `orm:"unit_count" json:"unitCount" dc:"关联广告组数"`                       // 关联广告组数
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                         // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                         // 更新时间
	DeletedAt          *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                         // 删除时间
}

type KsAdvertiserCommonAssetTitleListRes struct {
	Id                 int         `json:"id" dc:""`
	Title              string      `json:"title" dc:"标题"`
	CategoryId         int         `json:"categoryId" dc:"标题分类ID"`
	CategoryName       string      `json:"categoryName" dc:"标题分类名称"`
	UserId             int         `json:"userId" dc:"创建者"`
	UserName           string      `json:"userName" dc:"创建者名称"`
	Last3DayCost       float64     `json:"last3DayCost" dc:"近3日消耗"`
	Last30DayCost      float64     `json:"last30DayCost" dc:"近30日消耗"`
	Last3DayClickRate  float64     `json:"last3DayClickRate" dc:"近3日点击率"`
	Last30DayClickRate float64     `json:"last30DayClickRate" dc:"近30日点击率"`
	UnitCount          int         `json:"unitCount" dc:"关联广告组数"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt          *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// KsAdvertiserCommonAssetTitleSearchReq 分页请求参数
type KsAdvertiserCommonAssetTitleSearchReq struct {
	comModel.PageReq
	Title       string   `p:"title" dc:"标题"`
	Titles      []string `p:"titles"  dc:"标题列表"`
	CategoryIds []int    `p:"categoryIds"  dc:"标题分类ID列表"`
	UserIds     []int    `p:"userIds" dc:"创建者"`
}

// KsAdvertiserCommonAssetTitleSearchRes 列表返回结果
type KsAdvertiserCommonAssetTitleSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserCommonAssetTitleListRes `json:"list"`
}

// KsAdvertiserCommonAssetTitleBatchAddReq 添加操作请求参数
type KsAdvertiserCommonAssetTitleBatchAddReq struct {
	Titles       []string `p:"titles"  dc:"标题列表"`
	CategoryId   int      `p:"categoryId"  dc:"标题分类ID 选择已有标题必填"`
	CategoryName string   `p:"categoryName"  dc:"标题分类名称 新增标题必填"`
}

// KsAdvertiserCommonAssetTitleAddReq 修改操作请求参数
type KsAdvertiserCommonAssetTitleAddReq struct {
	Title      string `p:"title"  dc:"标题"`
	CategoryId int    `p:"categoryId"  dc:"标题分类ID"`
	UserId     int    `p:"userId"  dc:"创建者"`
}

// KsAdvertiserCommonAssetTitleEditReq 修改操作请求参数
type KsAdvertiserCommonAssetTitleEditReq struct {
	Id           int    `p:"id" v:"required#主键ID不能为空" dc:""`
	Title        string `p:"title"  dc:"标题"`
	CategoryId   int    `p:"categoryId"  dc:"标题分类ID 选择已有标题必填"`
	CategoryName string `p:"categoryName"  dc:"标题分类名称 新增标题必填"`
}

// KsAdvertiserCommonAssetTitleBatchEditReq 批量修改分类操作请求参数
type KsAdvertiserCommonAssetTitleBatchEditReq struct {
	Ids          []int  `p:"ids" v:"required#主键ID不能为空" dc:""`
	CategoryId   int    `p:"categoryId"  dc:"标题分类ID 选择已有标题必填"`
	CategoryName string `p:"categoryName"  dc:"标题分类名称 新增标题必填"`
}

// KsAdvertiserCommonAssetTitleUserListReq 分页请求参数
type KsAdvertiserCommonAssetTitleUserListReq struct {
	comModel.PageReq
	UserName string `p:"userName" dc:"创建者名称"`
}

// KsAdvertiserCommonAssetTitleUserListRes 列表返回结果
type KsAdvertiserCommonAssetTitleUserListRes struct {
	comModel.ListRes
	List []*KsAdvertiserCommonAssetTitleUserInfoRes `json:"list"`
}

type KsAdvertiserCommonAssetTitleUserInfoRes struct {
	UserId   int    `json:"userId" dc:"创建者"`
	UserName string `json:"userName" dc:"创建者名称"`
}
