// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: api/v1/ad/ks_advertiser_strategy_generage.go
// 生成人：gfast
// desc:快手广告策略生成相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// KsAdvertiserStrategyGenerateReq 生成广告预览请求参数
type KsAdvertiserStrategyGenerateReq struct {
	g.Meta `path:"/generateAdPreview" tags:"快手广告搭建-生成广告" method:"post" summary:"快手广告搭建-生成广告预览"`
	commonApi.Author
	*model.KsAdvertiserStrategyGenerateReq
}

// KsAdvertiserStrategyGenerateRes 生成广告预览返回结果
type KsAdvertiserStrategyGenerateRes struct {
	commonApi.EmptyRes
	*model.KsAdvertiserStrategyGenerateRes
}

// KsAdvertiserStrategyExecuteTask
type KsAdvertiserStrategyExecuteTaskReq struct {
	g.Meta `path:"/executeTask" tags:"快手广告搭建-执行任务" method:"post" summary:"快手广告搭建-执行任务"`
	commonApi.Author
	*model.AdExecuteTaskReq
}

type KsAdvertiserStrategyExecuteTaskRes struct {
	commonApi.EmptyRes
}

// QuerySeriesAuthUserListReq 获取授权的短剧作者列表请求参数
type QuerySeriesAuthUserListReq struct {
	g.Meta `path:"/querySeriesAuthUserList" tags:"快手广告搭建-短剧管理" method:"post" summary:"获取授权的短剧作者列表"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiser_id" v:"required#广告主ID必须" dc:"广告主ID"`
}

// QuerySeriesAuthUserListRes 获取授权的短剧作者列表返回结果
type QuerySeriesAuthUserListRes struct {
	g.Meta `mime:"application/json"`
	Data   []ksApi.QuerySeriesAuthUserListData `json:"data" dc:"短剧作者列表"`
}

// QuerySeriesListReq 查询授权短剧列表请求参数
type QuerySeriesListReq struct {
	g.Meta `path:"/querySeriesList" tags:"快手广告搭建-短剧管理" method:"post" summary:"查询授权短剧列表"`
	commonApi.Author
	*model.QuerySeriesListReq
}

// QuerySeriesListRes 查询授权短剧列表返回结果
type QuerySeriesListRes struct {
	g.Meta `mime:"application/json"`
	Data   []ksApi.MapiSeriesInfoSnake `json:"data" dc:"短剧列表"`
}

// QuerySeriesEpisodeListReq 查询短剧剧集列表请求参数
type QuerySeriesEpisodeListReq struct {
	g.Meta `path:"/querySeriesEpisodeList" tags:"快手广告搭建-短剧管理" method:"post" summary:"查询短剧剧集列表"`
	commonApi.Author
	*model.QuerySeriesEpisodeListReq
}

// QuerySeriesEpisodeListRes 查询短剧剧集列表返回结果
type QuerySeriesEpisodeListRes struct {
	g.Meta `mime:"application/json"`
	Data   []ksApi.MapiEpisodeInfoSnake `json:"data" dc:"剧集列表"`
}

// QuerySeriesPayModeTypeReq 查询短剧付费模式请求参数
type QuerySeriesPayModeTypeReq struct {
	g.Meta `path:"/querySeriesPayModeType" tags:"快手广告搭建-短剧管理" method:"post" summary:"查询短剧付费模式"`
	commonApi.Author
	*model.QuerySeriesPayModeTypeReq
}

// QuerySeriesPayModeTypeRes 查询短剧付费模式返回结果
type QuerySeriesPayModeTypeRes struct {
	g.Meta `mime:"application/json"`
	Data   []ksApi.MapiSeriesPayModeInfoSnake `json:"data" dc:"付费模式列表"`
}

// QuerySeriesPayModeTemplateReq 查询短剧付费模板请求参数
type QuerySeriesPayModeTemplateReq struct {
	g.Meta `path:"/querySeriesPayModeTemplate" tags:"快手广告搭建-短剧管理" method:"post" summary:"查询短剧付费模板"`
	commonApi.Author
	*model.QuerySeriesPayModeTemplateReq
}

// QuerySeriesPayModeTemplateRes 查询短剧付费模板返回结果
type QuerySeriesPayModeTemplateRes struct {
	g.Meta `mime:"application/json"`
	Data   []ksApi.MapiSeriesPayModeTemplateInfoSnake `json:"data" dc:"付费模板列表"`
}

// QueryProductListReq 查询商品列表请求参数
type QueryProductListReq struct {
	g.Meta `path:"/queryProductList" tags:"快手广告搭建-商品管理" method:"post" summary:"查询商品列表"`
	commonApi.Author
	*model.QueryProductListReq
}

// QueryProductListRes 查询商品列表返回结果
type QueryProductListRes struct {
	g.Meta `mime:"application/json"`
	*ksApi.ProductBatchQueryResponse
}

// QueryProductLibraryListReq 查询商品库列表请求参数
type QueryProductLibraryListReq struct {
	g.Meta `path:"/queryProductLibraryList" tags:"快手广告搭建-商品管理" method:"post" summary:"查询商品库列表"`
	commonApi.Author
	*model.QueryProductLibraryListReq
}

// QueryProductLibraryListRes 查询商品库列表返回结果
type QueryProductLibraryListRes struct {
	g.Meta `mime:"application/json"`
	*ksApi.LibraryListResponse
}

// QueryCreativeActionBarTextReq 查询行动号召按钮请求参数
type QueryCreativeActionBarTextReq struct {
	g.Meta `path:"/queryCreativeActionBarText" tags:"快手广告搭建-创意管理" method:"post" summary:"查询行动号召按钮"`
	commonApi.Author
	*model.QueryCreativeActionBarTextReq
}

// QueryCreativeActionBarTextRes 查询行动号召按钮返回结果
type QueryCreativeActionBarTextRes struct {
	g.Meta `mime:"application/json"`
	Data   []string `json:"data" dc:"行动号召按钮文案列表"`
}

// QueryToolExposeTagsReq 查询创意推荐理由请求参数
type QueryToolExposeTagsReq struct {
	g.Meta `path:"/queryToolExposeTags" tags:"快手广告搭建-创意管理" method:"post" summary:"查询创意推荐理由"`
	commonApi.Author
	*model.QueryToolExposeTagsReq
}

// QueryToolExposeTagsRes 查询创意推荐理由返回结果
type QueryToolExposeTagsRes struct {
	g.Meta `mime:"application/json"`
	Data   []string `json:"data" dc:"创意推荐理由列表"`
}

// QueryCreativeCategoryReq 查询创意分类请求参数
type QueryCreativeCategoryReq struct {
	g.Meta `path:"/queryCreativeCategory" tags:"快手广告搭建-创意管理" method:"post" summary:"查询创意分类"`
	commonApi.Author
	*model.QueryCreativeCategoryReq
}

// QueryCreativeCategoryRes 查询创意分类返回结果
type QueryCreativeCategoryRes struct {
	g.Meta `mime:"application/json"`
	Data   []*model.QueryCreativeCategoryRes `json:"data" dc:"创意分类树形列表"`
}

// QueryRoiRatioReq 查询ROI系数参数
type QueryRoiRatioReq struct {
	g.Meta `path:"/queryRoiRatio" tags:"快手广告搭建-创意管理" method:"post" summary:"查询ROI系数"`
	commonApi.Author
	OcpxActionType int `p:"ocpxActionType" v:"required#请选择转化目标" dc:"转化目标"`
}

// QueryRoiRatioRes 查询创意分类返回结果
type QueryRoiRatioRes struct {
	g.Meta   `mime:"application/json"`
	RoiRatio float64 `json:"roiRatio" dc:"roi系数"`
}
