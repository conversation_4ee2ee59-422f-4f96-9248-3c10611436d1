/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// ProjectWeekScheduleUpdateV3ApiService ProjectWeekScheduleUpdateV3Api service
type ProjectWeekScheduleUpdateV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ProjectWeekScheduleUpdateV30Request
}

func (r *ProjectWeekScheduleUpdateV3ApiService) SetCfg(cfg *conf.Configuration) *ProjectWeekScheduleUpdateV3ApiService {
	r.cfg = cfg
	return r
}

func (r *ProjectWeekScheduleUpdateV3ApiService) ProjectWeekScheduleUpdateV30Request(projectWeekScheduleUpdateV30Request models.ProjectWeekScheduleUpdateV30Request) *ProjectWeekScheduleUpdateV3ApiService {
	r.Request = &projectWeekScheduleUpdateV30Request
	return r
}

func (r *ProjectWeekScheduleUpdateV3ApiService) AccessToken(accessToken string) *ProjectWeekScheduleUpdateV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ProjectWeekScheduleUpdateV3ApiService) Do() (data *models.ProjectWeekScheduleUpdateV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/project/week_schedule/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ProjectWeekScheduleUpdateV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ProjectWeekScheduleUpdateV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/project/week_schedule/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
