// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-23 15:51:09
// 生成路径: internal/app/ad/model/entity/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialUpload is the golang structure for table ad_material_upload.
type AdMaterialUpload struct {
	gmeta.Meta   `orm:"table:ad_material_upload, do:true"`
	Id           interface{} `orm:"id,primary" json:"id"`                       // 自增ID
	MediaType    interface{} `orm:"media_type" json:"mediaType"`                // 媒体类型：图片或视频 1 图片 2视频
	AdMaterialId interface{} `orm:"ad_material_id" json:"adMaterialId"`         // 素材id 关联ad_material 表格 material_id
	AdvertiserId interface{} `orm:"advertiser_id" json:"advertiserId"`          // 广告账户id
	MediaId      interface{} `orm:"media_id" json:"mediaId"`                    // 图片/视频ID
	MaterialName interface{} `orm:"material_name" json:"materialName" dc:"素材名"` // 素材名
	Size         interface{} `orm:"size" json:"size"`                           // 媒体文件大小
	Width        interface{} `orm:"width" json:"width"`                         // 宽度
	Height       interface{} `orm:"height" json:"height"`                       // 高度
	AdType       interface{} `orm:"ad_type" json:"adType"`                      // 1巨量  2快手
	Url          interface{} `orm:"url" json:"url"`                             // 图片预览地址或视频地址
	Format       interface{} `orm:"format" json:"format"`                       // 图片格式
	Signature    interface{} `orm:"signature" json:"signature"`                 // 图片md5 或视频md5
	MaterialId   interface{} `orm:"material_id" json:"materialId"`              // 素材id，即多合一报表中的素材id，唯一对应一个素材id
	Duration     interface{} `orm:"duration" json:"duration"`                   // 视频时长，如果是视频，则填充此字段
	TaskId       interface{} `orm:"task_id" json:"taskId"`                      // 任务id
	NewStatus    interface{} `orm:"new_status" json:"newStatus"`                // 0：视频库删除，1：可用状态，2：转码中，3：审核中，11：转码失败，12：审核失败，13：已屏蔽，14：视频在客户端删除
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt"`                // 创建时间
	UpdatedAt    *gtime.Time `orm:"updated_at" json:"updatedAt"`                // 更新时间
}
