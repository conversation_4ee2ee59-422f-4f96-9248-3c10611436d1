// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/logic/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserCommonAssetTitle(New())
}

func New() service.IKsAdvertiserCommonAssetTitle {
	return &sKsAdvertiserCommonAssetTitle{}
}

type sKsAdvertiserCommonAssetTitle struct{}

func (s *sKsAdvertiserCommonAssetTitle) List(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleSearchReq) (listRes *model.KsAdvertiserCommonAssetTitleSearchRes, err error) {
	listRes = new(model.KsAdvertiserCommonAssetTitleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id").
			LeftJoin("ks_advertiser_common_asset_category c", "ad.category_id = c.id")
		if len(req.UserIds) > 0 {
			m = m.WhereIn("ad."+dao.KsAdvertiserCommonAssetTitle.Columns().UserId, req.UserIds)
		} else if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.KsAdvertiserCommonAssetTitle.Columns().UserId, userIds)
		}
		if req.Title != "" {
			m = m.WhereLike("ad."+dao.KsAdvertiserCommonAssetTitle.Columns().Title, "%"+req.Title+"%")
		}
		if len(req.Titles) > 0 {
			m = m.WhereIn("ad."+dao.KsAdvertiserCommonAssetTitle.Columns().Title, req.Titles)
		}
		if len(req.CategoryIds) > 0 {
			m = m.WhereIn("ad."+dao.KsAdvertiserCommonAssetTitle.Columns().CategoryId, req.CategoryIds)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "ad.id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Fields("ad.id as id").
			Fields("ad.title as title").
			Fields("ad.category_id as categoryId").
			Fields("c.category as categoryName").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.last_3_day_cost as last3DayCost").
			Fields("ad.last_30_day_cost as last30DayCost").
			Fields("ad.last_3_day_click_rate as last3DayClickRate").
			Fields("ad.last_30_day_click_rate as last30DayClickRate").
			Fields("ad.unit_count as unitCount").
			Fields("ad.created_at as createdAt").
			Fields("ad.updated_at as updatedAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) GetById(ctx context.Context, id int) (res *model.KsAdvertiserCommonAssetTitleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).WithAll().Where(dao.KsAdvertiserCommonAssetTitle.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) Add(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleBatchAddReq) (err error) {
	return g.Try(ctx, func(ctx context.Context) {
		if len(req.Titles) == 0 {
			return
		}
		userId := sysService.Context().GetUserId(ctx)
		categoryId := s.GetCategoryId(ctx, req.CategoryName, req.CategoryId)
		batchAddReq := make([]*model.KsAdvertiserCommonAssetTitleAddReq, len(req.Titles))
		for k, v := range req.Titles {
			batchAddReq[k] = &model.KsAdvertiserCommonAssetTitleAddReq{
				Title:      v,
				CategoryId: categoryId,
				UserId:     int(userId),
			}
		}
		err = s.BatchAdd(ctx, batchAddReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
}

func (s *sKsAdvertiserCommonAssetTitle) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserCommonAssetTitleAddReq) error {
	return g.Try(ctx, func(ctx context.Context) {
		if len(batchReq) == 0 {
			return
		}
		data := make([]*do.KsAdvertiserCommonAssetTitle, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.KsAdvertiserCommonAssetTitle{
				Title:      v.Title,
				CategoryId: v.CategoryId,
				UserId:     v.UserId,
			}
		}
		_, err := dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
}

func (s *sKsAdvertiserCommonAssetTitle) Edit(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userId := sysService.Context().GetUserId(ctx)
		categoryId := s.GetCategoryId(ctx, req.CategoryName, req.CategoryId)
		_, err = dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).WherePri(req.Id).Update(do.KsAdvertiserCommonAssetTitle{
			Title:      req.Title,
			CategoryId: categoryId,
			UserId:     userId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) BatchEditCategory(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleBatchEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		categoryId := s.GetCategoryId(ctx, req.CategoryName, req.CategoryId)
		_, err = dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).WherePri(req.Ids).Update(do.KsAdvertiserCommonAssetTitle{
			CategoryId: categoryId,
		})
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).Delete(dao.KsAdvertiserCommonAssetTitle.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) GetUserList(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleUserListReq) (listRes *model.KsAdvertiserCommonAssetTitleUserListRes, err error) {
	listRes = new(model.KsAdvertiserCommonAssetTitleUserListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserCommonAssetTitle.Ctx(ctx).As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad.user_id", userIds)
		}
		if req.UserName != "" {
			m = m.WhereLike("u.user_name", "%"+req.UserName+"%")
		}
		listRes.Total, err = m.Group("ad.user_id").Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "ad.user_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Page(req.PageNum, req.PageSize).
			Group("ad.user_id").
			Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetTitle) GetCategoryId(ctx context.Context, categoryName string, categoryId int) int {
	if categoryName != "" {
		newCategoryId, err := service.KsAdvertiserCommonAssetCategory().Add(ctx, &model.KsAdvertiserCommonAssetCategoryAddReq{
			Category: categoryName,
		})
		liberr.ErrIsNil(ctx, err, "新增分类失败")
		categoryId = int(newCategoryId)
	}
	return categoryId
}
