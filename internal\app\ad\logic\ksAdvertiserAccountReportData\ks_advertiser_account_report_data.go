// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-12 10:44:33
// 生成路径: internal/app/ad/logic/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/task"
	"slices"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserAccountReportData(New())
}

func New() service.IKsAdvertiserAccountReportData {
	return &sKsAdvertiserAccountReportData{}
}

type sKsAdvertiserAccountReportData struct{}

func (s *sKsAdvertiserAccountReportData) List(ctx context.Context, req *model.KsAdvertiserAccountReportDataSearchReq) (listRes *model.KsAdvertiserAccountReportDataSearchRes, err error) {
	listRes = new(model.KsAdvertiserAccountReportDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserAccountInfoAnalytic.Ctx(ctx).As("a").
			LeftJoin("sys_user u", "a.owner = u.id")
		m = m.LeftJoin("ks_advertiser_account_report_data", "b",
			fmt.Sprintf("a.account_id = b.advertiser_id AND b.stat_date >= '%s' AND b.stat_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a.owner", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a.owner", userIds)
			}
		}
		if len(req.CorporationNames) > 0 {
			m = m.WhereIn("a.corporation_name", req.CorporationNames)
		}
		if len(req.AdvertiserNames) > 0 {
			m = m.WhereIn("a.account_name", req.AdvertiserNames)
		}
		if len(req.AdvertiserIds) > 0 {
			m = m.WhereIn("a.account_id", req.AdvertiserIds)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.KeyWord) > 0 {
			m = m.Where("a."+dao.KsAdvertiserAccountInfo.Columns().AccountName+" like ?", "%"+req.KeyWord+"%")
		}
		if req.AccountAutoManage != nil {
			m = m.Where("a.account_auto_manage", gconv.Int(req.AccountAutoManage))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		group := "a.account_id"
		order := "charge desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(group).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}

		// 获取默认字段（从结构体标签中提取）
		defaultFields := sysService.SUserPageTableConfig().GetDefaultFields(model.KsAdvertiserAccountReportDataListRes{})

		// 根据用户配置动态构建字段
		fields, err := sysService.SUserPageTableConfig().BuildFieldsByUserConfig(ctx,
			"/api/v1/ad/ksAdvertiserAccountReportData/list", model.KsAdvertiserAccountReportDataListRes{}, defaultFields)
		liberr.ErrIsNil(ctx, err, "构建动态字段失败")
		if len(fields) > 0 {
			// 特殊字段，没有在自定义列中
			fields = append(fields, "ANY_VALUE(a.account_auto_manage) as accountAutoManage")
		}

		err = m.Fields(fields).Group(group).Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 计算汇总
		err = m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
	})
	return
}

func (s *sKsAdvertiserAccountReportData) Add(ctx context.Context, req *model.KsAdvertiserAccountReportDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAccountReportData.Ctx(ctx).Insert(do.KsAdvertiserAccountReportData{
			AdvertiserId:                req.AdvertiserId,
			StatDate:                    req.StatDate,
			PrivateMessageSentCost:      req.PrivateMessageSentCost,
			PrivateMessageSentRatio:     req.PrivateMessageSentRatio,
			PrivateMessageSentCnt:       req.PrivateMessageSentCnt,
			LeadsSubmitCost:             req.LeadsSubmitCost,
			LeadsSubmitCntRatio:         req.LeadsSubmitCntRatio,
			LeadsSubmitCnt:              req.LeadsSubmitCnt,
			PlayedNum:                   req.PlayedNum,
			PlayedEnd:                   req.PlayedEnd,
			PlayedFiveSeconds:           req.PlayedFiveSeconds,
			PlayedThreeSeconds:          req.PlayedThreeSeconds,
			Play3SRatio:                 req.Play3SRatio,
			Play5SRatio:                 req.Play5SRatio,
			PlayEndRatio:                req.PlayEndRatio,
			AdPhotoPlayed10S:            req.AdPhotoPlayed10S,
			AdPhotoPlayed2S:             req.AdPhotoPlayed2S,
			AdPhotoPlayed75Percent:      req.AdPhotoPlayed75Percent,
			AdPhotoPlayed75PercentRatio: req.AdPhotoPlayed75PercentRatio,
			AdPhotoPlayed10SRatio:       req.AdPhotoPlayed10SRatio,
			AdPhotoPlayed2SRatio:        req.AdPhotoPlayed2SRatio,
			MinigameIaaPurchaseAmountWeekByConversionRoi:     req.MinigameIaaPurchaseAmountWeekByConversionRoi,
			MinigameIaaPurchaseAmountThreeDayByConversionRoi: req.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
			MinigameIaaPurchaseAmountFirstDayRoi:             req.MinigameIaaPurchaseAmountFirstDayRoi,
			MinigameIaaPurchaseAmountWeekByConversion:        req.MinigameIaaPurchaseAmountWeekByConversion,
			MinigameIaaPurchaseAmountThreeDayByConversion:    req.MinigameIaaPurchaseAmountThreeDayByConversion,
			MinigameIaaPurchaseAmountFirstDay:                req.MinigameIaaPurchaseAmountFirstDay,
			MinigameIaaPurchaseRoi:                           req.MinigameIaaPurchaseRoi,
			MinigameIaaPurchaseAmount:                        req.MinigameIaaPurchaseAmount,
			MinigameIaaPurchaseAmount30DayByConversionRoi:    req.MinigameIaaPurchaseAmount30DayByConversionRoi,
			MinigameIaaPurchaseAmount15DayByConversionRoi:    req.MinigameIaaPurchaseAmount15DayByConversionRoi,
			MinigameIaaPurchaseAmount30DayByConversion:       req.MinigameIaaPurchaseAmount30DayByConversion,
			MinigameIaaPurchaseAmount15DayByConversion:       req.MinigameIaaPurchaseAmount15DayByConversion,
			MmuEffectiveCustomerAcquisition7DCnt:             req.MmuEffectiveCustomerAcquisition7DCnt,
			MmuEffectiveCustomerAcquisitionCnt:               req.MmuEffectiveCustomerAcquisitionCnt,
			EffectiveCustomerAcquisition7DRatio:              req.EffectiveCustomerAcquisition7DRatio,
			EffectiveCustomerAcquisition7DCost:               req.EffectiveCustomerAcquisition7DCost,
			EffectiveCustomerAcquisition7DCnt:                req.EffectiveCustomerAcquisition7DCnt,
			EventPay30DayOverallRoi:                          req.EventPay30DayOverallRoi,
			EventPay15DayOverallRoi:                          req.EventPay15DayOverallRoi,
			EventPayPurchaseAmount15DayByConversion:          req.EventPayPurchaseAmount15DayByConversion,
			EventPayPurchaseAmount30DayByConversion:          req.EventPayPurchaseAmount30DayByConversion,
			EventPayFirstDay:                                 req.EventPayFirstDay,
			EventPayPurchaseAmountFirstDay:                   req.EventPayPurchaseAmountFirstDay,
			EventPayFirstDayRoi:                              req.EventPayFirstDayRoi,
			EventPay:                                         req.EventPay,
			EventPayPurchaseAmount:                           req.EventPayPurchaseAmount,
			EventPayRoi:                                      req.EventPayRoi,
			EventPayPurchaseAmountOneDay:                     req.EventPayPurchaseAmountOneDay,
			EventPayPurchaseAmountOneDayByConversion:         req.EventPayPurchaseAmountOneDayByConversion,
			EventPayPurchaseAmountOneDayByConversionRoi:      req.EventPayPurchaseAmountOneDayByConversionRoi,
			EventPayPurchaseAmountOneDayRoi:                  req.EventPayPurchaseAmountOneDayRoi,
			EventPayWeightedPurchaseAmount:                   req.EventPayWeightedPurchaseAmount,
			EventPayWeightedPurchaseAmountFirstDay:           req.EventPayWeightedPurchaseAmountFirstDay,
			Charge:                                           req.Charge,
			Show:                                             req.Show,
			Aclick:                                           req.Aclick,
			Bclick:                                           req.Bclick,
			AdShow:                                           req.AdShow,
			Share:                                            req.Share,
			Comment:                                          req.Comment,
			Like:                                             req.Like,
			Follow:                                           req.Follow,
			CancelLike:                                       req.CancelLike,
			CancelFollow:                                     req.CancelFollow,
			Report:                                           req.Report,
			Block:                                            req.Block,
			Negative:                                         req.Negative,
			Activation:                                       req.Activation,
			DownloadStarted:                                  req.DownloadStarted,
			DownloadCompleted:                                req.DownloadCompleted,
			DownloadInstalled:                                req.DownloadInstalled,
			ClickConversionRatio:                             req.ClickConversionRatio,
			ConversionCost:                                   req.ConversionCost,
			DownloadCompletedCost:                            req.DownloadCompletedCost,
			DownloadCompletedRatio:                           req.DownloadCompletedRatio,
			DownloadConversionRatio:                          req.DownloadConversionRatio,
			DownloadStartedCost:                              req.DownloadStartedCost,
			DownloadStartedRatio:                             req.DownloadStartedRatio,
			EventRegister:                                    req.EventRegister,
			EventRegisterCost:                                req.EventRegisterCost,
			EventRegisterRatio:                               req.EventRegisterRatio,
			EventJinJianApp:                                  req.EventJinJianApp,
			EventJinJianAppCost:                              req.EventJinJianAppCost,
			EventJinJianLandingPage:                          req.EventJinJianLandingPage,
			EventJinJianLandingPageCost:                      req.EventJinJianLandingPageCost,
			Jinjian0DCnt:                                     req.Jinjian0DCnt,
			Jinjian3DCnt:                                     req.Jinjian3DCnt,
			Jinjian0DCntCost:                                 req.Jinjian0DCntCost,
			Jinjian3DCntCost:                                 req.Jinjian3DCntCost,
			EventCreditGrantApp:                              req.EventCreditGrantApp,
			EventCreditGrantAppCost:                          req.EventCreditGrantAppCost,
			EventCreditGrantAppRatio:                         req.EventCreditGrantAppRatio,
			EventCreditGrantLandingPage:                      req.EventCreditGrantLandingPage,
			EventCreditGrantLandingPageCost:                  req.EventCreditGrantLandingPageCost,
			EventCreditGrantLandingRatio:                     req.EventCreditGrantLandingRatio,
			EventCreditGrantFirstDayApp:                      req.EventCreditGrantFirstDayApp,
			EventCreditGrantFirstDayAppCost:                  req.EventCreditGrantFirstDayAppCost,
			EventCreditGrantFirstDayAppRatio:                 req.EventCreditGrantFirstDayAppRatio,
			EventCreditGrantFirstDayLandingPage:              req.EventCreditGrantFirstDayLandingPage,
			EventCreditGrantFirstDayLandingPageCost:          req.EventCreditGrantFirstDayLandingPageCost,
			EventCreditGrantFirstDayLandingPageRatio:         req.EventCreditGrantFirstDayLandingPageRatio,
			CreditGrant0DCnt:                                 req.CreditGrant0DCnt,
			CreditGrant3DCnt:                                 req.CreditGrant3DCnt,
			CreditGrant0DCntCost:                             req.CreditGrant0DCntCost,
			CreditGrant3DCntCost:                             req.CreditGrant3DCntCost,
			CreditGrant0DCntRatio:                            req.CreditGrant0DCntRatio,
			CreditGrant3DCntRatio:                            req.CreditGrant3DCntRatio,
			EventOrderSubmit:                                 req.EventOrderSubmit,
			EventOrderPaid:                                   req.EventOrderPaid,
			EventOrderPaidPurchaseAmount:                     req.EventOrderPaidPurchaseAmount,
			EventOrderPaidCost:                               req.EventOrderPaidCost,
			EventOrderPaidRoi:                                req.EventOrderPaidRoi,
			OrderSubmitAmt:                                   req.OrderSubmitAmt,
			FormCount:                                        req.FormCount,
			FormCost:                                         req.FormCost,
			FormActionRatio:                                  req.FormActionRatio,
			Submit:                                           req.Submit,
			EventValidClues:                                  req.EventValidClues,
			EventValidCluesCost:                              req.EventValidCluesCost,
			EventConsultationValidRetained:                   req.EventConsultationValidRetained,
			EventConsultationValidRetainedCost:               req.EventConsultationValidRetainedCost,
			EventConsultationValidRetainedRatio:              req.EventConsultationValidRetainedRatio,
			EventConversionClickCost:                         req.EventConversionClickCost,
			EventConversionClickRatio:                        req.EventConversionClickRatio,
			EventPreComponentConsultationValidRetained:       req.EventPreComponentConsultationValidRetained,
			EventAdWatch10Times:                              req.EventAdWatch10Times,
			EventAdWatch10TimesCost:                          req.EventAdWatch10TimesCost,
			EventAdWatch10TimesRatio:                         req.EventAdWatch10TimesRatio,
			EventAdWatch20Times:                              req.EventAdWatch20Times,
			EventAdWatch20TimesCost:                          req.EventAdWatch20TimesCost,
			EventAdWatch20TimesRatio:                         req.EventAdWatch20TimesRatio,
			EventAdWatch5Times:                               req.EventAdWatch5Times,
			EventAdWatch5TimesCost:                           req.EventAdWatch5TimesCost,
			EventAdWatch5TimesRatio:                          req.EventAdWatch5TimesRatio,
			EventWatchAppAd:                                  req.EventWatchAppAd,
			EventAdWatchTimes:                                req.EventAdWatchTimes,
			EventAdWatchTimesRatio:                           req.EventAdWatchTimesRatio,
			EventAdWatchTimesCost:                            req.EventAdWatchTimesCost,
			EventMakingCalls:                                 req.EventMakingCalls,
			EventMakingCallsCost:                             req.EventMakingCallsCost,
			EventMakingCallsRatio:                            req.EventMakingCallsRatio,
			EventGetThrough:                                  req.EventGetThrough,
			EventGetThroughCost:                              req.EventGetThroughCost,
			EventGetThroughRatio:                             req.EventGetThroughRatio,
			EventPhoneGetThrough:                             req.EventPhoneGetThrough,
			EventOutboundCall:                                req.EventOutboundCall,
			EventOutboundCallCost:                            req.EventOutboundCallCost,
			EventOutboundCallRatio:                           req.EventOutboundCallRatio,
			EventWechatQrCodeLinkClick:                       req.EventWechatQrCodeLinkClick,
			EventAddWechat:                                   req.EventAddWechat,
			EventAddWechatCost:                               req.EventAddWechatCost,
			EventAddWechatRatio:                              req.EventAddWechatRatio,
			EventWechatConnected:                             req.EventWechatConnected,
			EventAudition:                                    req.EventAudition,
			EventButtonClick:                                 req.EventButtonClick,
			EventButtonClickCost:                             req.EventButtonClickCost,
			EventButtonClickRatio:                            req.EventButtonClickRatio,
			EventMultiConversion:                             req.EventMultiConversion,
			EventMultiConversionRatio:                        req.EventMultiConversionRatio,
			EventMultiConversionCost:                         req.EventMultiConversionCost,
			EventAddShoppingCart:                             req.EventAddShoppingCart,
			EventAddShoppingCartCost:                         req.EventAddShoppingCartCost,
			EventIntentionConfirmed:                          req.EventIntentionConfirmed,
			EventOrderSuccessed:                              req.EventOrderSuccessed,
			EventPhoneCardActivate:                           req.EventPhoneCardActivate,
			EventMeasurementHouse:                            req.EventMeasurementHouse,
			EventAppInvoked:                                  req.EventAppInvoked,
			EventAppInvokedCost:                              req.EventAppInvokedCost,
			EventAppInvokedRatio:                             req.EventAppInvokedRatio,
			EventNextDayStayCost:                             req.EventNextDayStayCost,
			EventNextDayStayRatio:                            req.EventNextDayStayRatio,
			EventNextDayStay:                                 req.EventNextDayStay,
			PhotoClick:                                       req.PhotoClick,
			PhotoClickRatio:                                  req.PhotoClickRatio,
			PhotoClickCost:                                   req.PhotoClickCost,
			ActionRatio:                                      req.ActionRatio,
			ActionNewRatio:                                   req.ActionNewRatio,
			ActionCost:                                       req.ActionCost,
			Impression1KCost:                                 req.Impression1KCost,
			Click1KCost:                                      req.Click1KCost,
			ApproxPayCost:                                    req.ApproxPayCost,
			ApproxPayCount:                                   req.ApproxPayCount,
			ApproxPayRatio:                                   req.ApproxPayRatio,
			LiveEventGoodsView:                               req.LiveEventGoodsView,
			LivePlayed3S:                                     req.LivePlayed3S,
			AdProductCnt:                                     req.AdProductCnt,
			EventGoodsView:                                   req.EventGoodsView,
			EventGoodsViewCost:                               req.EventGoodsViewCost,
			MerchantRecoFans:                                 req.MerchantRecoFans,
			MerchantRecoFansCost:                             req.MerchantRecoFansCost,
			EventOrderAmountRoi:                              req.EventOrderAmountRoi,
			EventNewUserPay:                                  req.EventNewUserPay,
			EventNewUserPayCost:                              req.EventNewUserPayCost,
			EventNewUserPayRatio:                             req.EventNewUserPayRatio,
			EventNewUserJinjianApp:                           req.EventNewUserJinjianApp,
			EventNewUserJinjianAppCost:                       req.EventNewUserJinjianAppCost,
			EventNewUserJinjianAppRoi:                        req.EventNewUserJinjianAppRoi,
			EventNewUserCreditGrantApp:                       req.EventNewUserCreditGrantApp,
			EventNewUserCreditGrantAppCost:                   req.EventNewUserCreditGrantAppCost,
			EventNewUserCreditGrantAppRoi:                    req.EventNewUserCreditGrantAppRoi,
			EventNewUserJinjianPage:                          req.EventNewUserJinjianPage,
			EventNewUserJinjianPageCost:                      req.EventNewUserJinjianPageCost,
			EventNewUserJinjianPageRoi:                       req.EventNewUserJinjianPageRoi,
			EventNewUserCreditGrantPage:                      req.EventNewUserCreditGrantPage,
			EventNewUserCreditGrantPageCost:                  req.EventNewUserCreditGrantPageCost,
			EventNewUserCreditGrantPageRoi:                   req.EventNewUserCreditGrantPageRoi,
			EventAppointForm:                                 req.EventAppointForm,
			EventAppointFormCost:                             req.EventAppointFormCost,
			EventAppointFormRatio:                            req.EventAppointFormRatio,
			EventAppointJumpClick:                            req.EventAppointJumpClick,
			EventAppointJumpClickCost:                        req.EventAppointJumpClickCost,
			EventAppointJumpClickRatio:                       req.EventAppointJumpClickRatio,
			UnionEventPayPurchaseAmount7D:                    req.UnionEventPayPurchaseAmount7D,
			UnionEventPayPurchaseAmount7DRoi:                 req.UnionEventPayPurchaseAmount7DRoi,
			EventDspGiftForm:                                 req.EventDspGiftForm,
			EventCreditCardRecheck:                           req.EventCreditCardRecheck,
			EventCreditCardRecheckFirstDay:                   req.EventCreditCardRecheckFirstDay,
			KeyAction:                                        req.KeyAction,
			KeyActionCost:                                    req.KeyActionCost,
			KeyActionRatio:                                   req.KeyActionRatio,
			KeyInappAction0DCnt:                              req.KeyInappAction0DCnt,
			KeyInappAction3DCnt:                              req.KeyInappAction3DCnt,
			KeyInappAction0DCntCost:                          req.KeyInappAction0DCntCost,
			KeyInappAction3DCntCost:                          req.KeyInappAction3DCntCost,
			KeyInappAction0DCntRatio:                         req.KeyInappAction0DCntRatio,
			KeyInappAction3DCntRatio:                         req.KeyInappAction3DCntRatio,
			DrawCreditLine0DCnt:                              req.DrawCreditLine0DCnt,
			DrawCreditLine0DCntCost:                          req.DrawCreditLine0DCntCost,
			DrawCreditLine0DCntRatio:                         req.DrawCreditLine0DCntRatio,
			EventNoIntention:                                 req.EventNoIntention,
			AdScene:                                          req.AdScene,
			AdScene2:                                         req.AdScene2,
			PlacementType:                                    req.PlacementType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserAccountReportData) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserAccountReportDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.KsAdvertiserAccountReportData, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.KsAdvertiserAccountReportData{
				AdvertiserId:                v.AdvertiserId,
				StatDate:                    v.StatDate,
				PrivateMessageSentCost:      v.PrivateMessageSentCost,
				PrivateMessageSentRatio:     v.PrivateMessageSentRatio,
				PrivateMessageSentCnt:       v.PrivateMessageSentCnt,
				LeadsSubmitCost:             v.LeadsSubmitCost,
				LeadsSubmitCntRatio:         v.LeadsSubmitCntRatio,
				LeadsSubmitCnt:              v.LeadsSubmitCnt,
				PlayedNum:                   v.PlayedNum,
				PlayedEnd:                   v.PlayedEnd,
				PlayedFiveSeconds:           v.PlayedFiveSeconds,
				PlayedThreeSeconds:          v.PlayedThreeSeconds,
				Play3SRatio:                 v.Play3SRatio,
				Play5SRatio:                 v.Play5SRatio,
				PlayEndRatio:                v.PlayEndRatio,
				AdPhotoPlayed10S:            v.AdPhotoPlayed10S,
				AdPhotoPlayed2S:             v.AdPhotoPlayed2S,
				AdPhotoPlayed75Percent:      v.AdPhotoPlayed75Percent,
				AdPhotoPlayed75PercentRatio: v.AdPhotoPlayed75PercentRatio,
				AdPhotoPlayed10SRatio:       v.AdPhotoPlayed10SRatio,
				AdPhotoPlayed2SRatio:        v.AdPhotoPlayed2SRatio,
				MinigameIaaPurchaseAmountWeekByConversionRoi:     v.MinigameIaaPurchaseAmountWeekByConversionRoi,
				MinigameIaaPurchaseAmountThreeDayByConversionRoi: v.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
				MinigameIaaPurchaseAmountFirstDayRoi:             v.MinigameIaaPurchaseAmountFirstDayRoi,
				MinigameIaaPurchaseAmountWeekByConversion:        v.MinigameIaaPurchaseAmountWeekByConversion,
				MinigameIaaPurchaseAmountThreeDayByConversion:    v.MinigameIaaPurchaseAmountThreeDayByConversion,
				MinigameIaaPurchaseAmountFirstDay:                v.MinigameIaaPurchaseAmountFirstDay,
				MinigameIaaPurchaseRoi:                           v.MinigameIaaPurchaseRoi,
				MinigameIaaPurchaseAmount:                        v.MinigameIaaPurchaseAmount,
				MinigameIaaPurchaseAmount30DayByConversionRoi:    v.MinigameIaaPurchaseAmount30DayByConversionRoi,
				MinigameIaaPurchaseAmount15DayByConversionRoi:    v.MinigameIaaPurchaseAmount15DayByConversionRoi,
				MinigameIaaPurchaseAmount30DayByConversion:       v.MinigameIaaPurchaseAmount30DayByConversion,
				MinigameIaaPurchaseAmount15DayByConversion:       v.MinigameIaaPurchaseAmount15DayByConversion,
				MmuEffectiveCustomerAcquisition7DCnt:             v.MmuEffectiveCustomerAcquisition7DCnt,
				MmuEffectiveCustomerAcquisitionCnt:               v.MmuEffectiveCustomerAcquisitionCnt,
				EffectiveCustomerAcquisition7DRatio:              v.EffectiveCustomerAcquisition7DRatio,
				EffectiveCustomerAcquisition7DCost:               v.EffectiveCustomerAcquisition7DCost,
				EffectiveCustomerAcquisition7DCnt:                v.EffectiveCustomerAcquisition7DCnt,
				EventPay30DayOverallRoi:                          v.EventPay30DayOverallRoi,
				EventPay15DayOverallRoi:                          v.EventPay15DayOverallRoi,
				EventPayPurchaseAmount15DayByConversion:          v.EventPayPurchaseAmount15DayByConversion,
				EventPayPurchaseAmount30DayByConversion:          v.EventPayPurchaseAmount30DayByConversion,
				EventPayFirstDay:                                 v.EventPayFirstDay,
				EventPayPurchaseAmountFirstDay:                   v.EventPayPurchaseAmountFirstDay,
				EventPayFirstDayRoi:                              v.EventPayFirstDayRoi,
				EventPay:                                         v.EventPay,
				EventPayPurchaseAmount:                           v.EventPayPurchaseAmount,
				EventPayRoi:                                      v.EventPayRoi,
				EventPayPurchaseAmountOneDay:                     v.EventPayPurchaseAmountOneDay,
				EventPayPurchaseAmountOneDayByConversion:         v.EventPayPurchaseAmountOneDayByConversion,
				EventPayPurchaseAmountOneDayByConversionRoi:      v.EventPayPurchaseAmountOneDayByConversionRoi,
				EventPayPurchaseAmountOneDayRoi:                  v.EventPayPurchaseAmountOneDayRoi,
				EventPayWeightedPurchaseAmount:                   v.EventPayWeightedPurchaseAmount,
				EventPayWeightedPurchaseAmountFirstDay:           v.EventPayWeightedPurchaseAmountFirstDay,
				Charge:                                           v.Charge,
				Show:                                             v.Show,
				Aclick:                                           v.Aclick,
				Bclick:                                           v.Bclick,
				AdShow:                                           v.AdShow,
				Share:                                            v.Share,
				Comment:                                          v.Comment,
				Like:                                             v.Like,
				Follow:                                           v.Follow,
				CancelLike:                                       v.CancelLike,
				CancelFollow:                                     v.CancelFollow,
				Report:                                           v.Report,
				Block:                                            v.Block,
				Negative:                                         v.Negative,
				Activation:                                       v.Activation,
				DownloadStarted:                                  v.DownloadStarted,
				DownloadCompleted:                                v.DownloadCompleted,
				DownloadInstalled:                                v.DownloadInstalled,
				ClickConversionRatio:                             v.ClickConversionRatio,
				ConversionCost:                                   v.ConversionCost,
				DownloadCompletedCost:                            v.DownloadCompletedCost,
				DownloadCompletedRatio:                           v.DownloadCompletedRatio,
				DownloadConversionRatio:                          v.DownloadConversionRatio,
				DownloadStartedCost:                              v.DownloadStartedCost,
				DownloadStartedRatio:                             v.DownloadStartedRatio,
				EventRegister:                                    v.EventRegister,
				EventRegisterCost:                                v.EventRegisterCost,
				EventRegisterRatio:                               v.EventRegisterRatio,
				EventJinJianApp:                                  v.EventJinJianApp,
				EventJinJianAppCost:                              v.EventJinJianAppCost,
				EventJinJianLandingPage:                          v.EventJinJianLandingPage,
				EventJinJianLandingPageCost:                      v.EventJinJianLandingPageCost,
				Jinjian0DCnt:                                     v.Jinjian0DCnt,
				Jinjian3DCnt:                                     v.Jinjian3DCnt,
				Jinjian0DCntCost:                                 v.Jinjian0DCntCost,
				Jinjian3DCntCost:                                 v.Jinjian3DCntCost,
				EventCreditGrantApp:                              v.EventCreditGrantApp,
				EventCreditGrantAppCost:                          v.EventCreditGrantAppCost,
				EventCreditGrantAppRatio:                         v.EventCreditGrantAppRatio,
				EventCreditGrantLandingPage:                      v.EventCreditGrantLandingPage,
				EventCreditGrantLandingPageCost:                  v.EventCreditGrantLandingPageCost,
				EventCreditGrantLandingRatio:                     v.EventCreditGrantLandingRatio,
				EventCreditGrantFirstDayApp:                      v.EventCreditGrantFirstDayApp,
				EventCreditGrantFirstDayAppCost:                  v.EventCreditGrantFirstDayAppCost,
				EventCreditGrantFirstDayAppRatio:                 v.EventCreditGrantFirstDayAppRatio,
				EventCreditGrantFirstDayLandingPage:              v.EventCreditGrantFirstDayLandingPage,
				EventCreditGrantFirstDayLandingPageCost:          v.EventCreditGrantFirstDayLandingPageCost,
				EventCreditGrantFirstDayLandingPageRatio:         v.EventCreditGrantFirstDayLandingPageRatio,
				CreditGrant0DCnt:                                 v.CreditGrant0DCnt,
				CreditGrant3DCnt:                                 v.CreditGrant3DCnt,
				CreditGrant0DCntCost:                             v.CreditGrant0DCntCost,
				CreditGrant3DCntCost:                             v.CreditGrant3DCntCost,
				CreditGrant0DCntRatio:                            v.CreditGrant0DCntRatio,
				CreditGrant3DCntRatio:                            v.CreditGrant3DCntRatio,
				EventOrderSubmit:                                 v.EventOrderSubmit,
				EventOrderPaid:                                   v.EventOrderPaid,
				EventOrderPaidPurchaseAmount:                     v.EventOrderPaidPurchaseAmount,
				EventOrderPaidCost:                               v.EventOrderPaidCost,
				EventOrderPaidRoi:                                v.EventOrderPaidRoi,
				OrderSubmitAmt:                                   v.OrderSubmitAmt,
				FormCount:                                        v.FormCount,
				FormCost:                                         v.FormCost,
				FormActionRatio:                                  v.FormActionRatio,
				Submit:                                           v.Submit,
				EventValidClues:                                  v.EventValidClues,
				EventValidCluesCost:                              v.EventValidCluesCost,
				EventConsultationValidRetained:                   v.EventConsultationValidRetained,
				EventConsultationValidRetainedCost:               v.EventConsultationValidRetainedCost,
				EventConsultationValidRetainedRatio:              v.EventConsultationValidRetainedRatio,
				EventConversionClickCost:                         v.EventConversionClickCost,
				EventConversionClickRatio:                        v.EventConversionClickRatio,
				EventPreComponentConsultationValidRetained:       v.EventPreComponentConsultationValidRetained,
				EventAdWatch10Times:                              v.EventAdWatch10Times,
				EventAdWatch10TimesCost:                          v.EventAdWatch10TimesCost,
				EventAdWatch10TimesRatio:                         v.EventAdWatch10TimesRatio,
				EventAdWatch20Times:                              v.EventAdWatch20Times,
				EventAdWatch20TimesCost:                          v.EventAdWatch20TimesCost,
				EventAdWatch20TimesRatio:                         v.EventAdWatch20TimesRatio,
				EventAdWatch5Times:                               v.EventAdWatch5Times,
				EventAdWatch5TimesCost:                           v.EventAdWatch5TimesCost,
				EventAdWatch5TimesRatio:                          v.EventAdWatch5TimesRatio,
				EventWatchAppAd:                                  v.EventWatchAppAd,
				EventAdWatchTimes:                                v.EventAdWatchTimes,
				EventAdWatchTimesRatio:                           v.EventAdWatchTimesRatio,
				EventAdWatchTimesCost:                            v.EventAdWatchTimesCost,
				EventMakingCalls:                                 v.EventMakingCalls,
				EventMakingCallsCost:                             v.EventMakingCallsCost,
				EventMakingCallsRatio:                            v.EventMakingCallsRatio,
				EventGetThrough:                                  v.EventGetThrough,
				EventGetThroughCost:                              v.EventGetThroughCost,
				EventGetThroughRatio:                             v.EventGetThroughRatio,
				EventPhoneGetThrough:                             v.EventPhoneGetThrough,
				EventOutboundCall:                                v.EventOutboundCall,
				EventOutboundCallCost:                            v.EventOutboundCallCost,
				EventOutboundCallRatio:                           v.EventOutboundCallRatio,
				EventWechatQrCodeLinkClick:                       v.EventWechatQrCodeLinkClick,
				EventAddWechat:                                   v.EventAddWechat,
				EventAddWechatCost:                               v.EventAddWechatCost,
				EventAddWechatRatio:                              v.EventAddWechatRatio,
				EventWechatConnected:                             v.EventWechatConnected,
				EventAudition:                                    v.EventAudition,
				EventButtonClick:                                 v.EventButtonClick,
				EventButtonClickCost:                             v.EventButtonClickCost,
				EventButtonClickRatio:                            v.EventButtonClickRatio,
				EventMultiConversion:                             v.EventMultiConversion,
				EventMultiConversionRatio:                        v.EventMultiConversionRatio,
				EventMultiConversionCost:                         v.EventMultiConversionCost,
				EventAddShoppingCart:                             v.EventAddShoppingCart,
				EventAddShoppingCartCost:                         v.EventAddShoppingCartCost,
				EventIntentionConfirmed:                          v.EventIntentionConfirmed,
				EventOrderSuccessed:                              v.EventOrderSuccessed,
				EventPhoneCardActivate:                           v.EventPhoneCardActivate,
				EventMeasurementHouse:                            v.EventMeasurementHouse,
				EventAppInvoked:                                  v.EventAppInvoked,
				EventAppInvokedCost:                              v.EventAppInvokedCost,
				EventAppInvokedRatio:                             v.EventAppInvokedRatio,
				EventNextDayStayCost:                             v.EventNextDayStayCost,
				EventNextDayStayRatio:                            v.EventNextDayStayRatio,
				EventNextDayStay:                                 v.EventNextDayStay,
				PhotoClick:                                       v.PhotoClick,
				PhotoClickRatio:                                  v.PhotoClickRatio,
				PhotoClickCost:                                   v.PhotoClickCost,
				ActionRatio:                                      v.ActionRatio,
				ActionNewRatio:                                   v.ActionNewRatio,
				ActionCost:                                       v.ActionCost,
				Impression1KCost:                                 v.Impression1KCost,
				Click1KCost:                                      v.Click1KCost,
				ApproxPayCost:                                    v.ApproxPayCost,
				ApproxPayCount:                                   v.ApproxPayCount,
				ApproxPayRatio:                                   v.ApproxPayRatio,
				LiveEventGoodsView:                               v.LiveEventGoodsView,
				LivePlayed3S:                                     v.LivePlayed3S,
				AdProductCnt:                                     v.AdProductCnt,
				EventGoodsView:                                   v.EventGoodsView,
				EventGoodsViewCost:                               v.EventGoodsViewCost,
				MerchantRecoFans:                                 v.MerchantRecoFans,
				MerchantRecoFansCost:                             v.MerchantRecoFansCost,
				EventOrderAmountRoi:                              v.EventOrderAmountRoi,
				EventNewUserPay:                                  v.EventNewUserPay,
				EventNewUserPayCost:                              v.EventNewUserPayCost,
				EventNewUserPayRatio:                             v.EventNewUserPayRatio,
				EventNewUserJinjianApp:                           v.EventNewUserJinjianApp,
				EventNewUserJinjianAppCost:                       v.EventNewUserJinjianAppCost,
				EventNewUserJinjianAppRoi:                        v.EventNewUserJinjianAppRoi,
				EventNewUserCreditGrantApp:                       v.EventNewUserCreditGrantApp,
				EventNewUserCreditGrantAppCost:                   v.EventNewUserCreditGrantAppCost,
				EventNewUserCreditGrantAppRoi:                    v.EventNewUserCreditGrantAppRoi,
				EventNewUserJinjianPage:                          v.EventNewUserJinjianPage,
				EventNewUserJinjianPageCost:                      v.EventNewUserJinjianPageCost,
				EventNewUserJinjianPageRoi:                       v.EventNewUserJinjianPageRoi,
				EventNewUserCreditGrantPage:                      v.EventNewUserCreditGrantPage,
				EventNewUserCreditGrantPageCost:                  v.EventNewUserCreditGrantPageCost,
				EventNewUserCreditGrantPageRoi:                   v.EventNewUserCreditGrantPageRoi,
				EventAppointForm:                                 v.EventAppointForm,
				EventAppointFormCost:                             v.EventAppointFormCost,
				EventAppointFormRatio:                            v.EventAppointFormRatio,
				EventAppointJumpClick:                            v.EventAppointJumpClick,
				EventAppointJumpClickCost:                        v.EventAppointJumpClickCost,
				EventAppointJumpClickRatio:                       v.EventAppointJumpClickRatio,
				UnionEventPayPurchaseAmount7D:                    v.UnionEventPayPurchaseAmount7D,
				UnionEventPayPurchaseAmount7DRoi:                 v.UnionEventPayPurchaseAmount7DRoi,
				EventDspGiftForm:                                 v.EventDspGiftForm,
				EventCreditCardRecheck:                           v.EventCreditCardRecheck,
				EventCreditCardRecheckFirstDay:                   v.EventCreditCardRecheckFirstDay,
				KeyAction:                                        v.KeyAction,
				KeyActionCost:                                    v.KeyActionCost,
				KeyActionRatio:                                   v.KeyActionRatio,
				KeyInappAction0DCnt:                              v.KeyInappAction0DCnt,
				KeyInappAction3DCnt:                              v.KeyInappAction3DCnt,
				KeyInappAction0DCntCost:                          v.KeyInappAction0DCntCost,
				KeyInappAction3DCntCost:                          v.KeyInappAction3DCntCost,
				KeyInappAction0DCntRatio:                         v.KeyInappAction0DCntRatio,
				KeyInappAction3DCntRatio:                         v.KeyInappAction3DCntRatio,
				DrawCreditLine0DCnt:                              v.DrawCreditLine0DCnt,
				DrawCreditLine0DCntCost:                          v.DrawCreditLine0DCntCost,
				DrawCreditLine0DCntRatio:                         v.DrawCreditLine0DCntRatio,
				EventNoIntention:                                 v.EventNoIntention,
				AdScene:                                          v.AdScene,
				AdScene2:                                         v.AdScene2,
				PlacementType:                                    v.PlacementType,
			}
		}
		_, err = dao.KsAdvertiserAccountReportData.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserAccountReportData) RunSyncKsAccountReportData(ctx context.Context, req *model.KsAdvertiserAccountReportDataSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncKsAccountReportData(ctx, startTime)
			if errors != nil {
				g.Log().Error(ctx, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sKsAdvertiserAccountReportData) SyncKsAccountReportDataTask(ctx context.Context) {
	err := task.ExecuteWithTimeout(
		ctx,
		commonConsts.PlatSyncKsAccountReportDataLock,
		(2*60*60)*time.Second,
		"同步昨天快手账户报表数据",
		"SyncKsAccountReportDataTask",
		func(ctx context.Context) error {
			yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
			return s.SyncKsAccountReportData(ctx, yesterday)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sKsAdvertiserAccountReportData) SyncTodayKsAccountReportDataTask(ctx context.Context) {
	err := task.ExecuteWithTimeout(
		ctx,
		commonConsts.PlatSyncTodayKsAccountReportDataLock,
		(30*60-5)*time.Second,
		"同步当天快手账户报表数据",
		"SyncTodayKsAccountReportDataTask",
		func(ctx context.Context) error {
			today := gtime.Now().Format("Y-m-d")
			return s.SyncKsAccountReportData(ctx, today)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SyncKsAccountReportData 同步快手账户报表数据
func (s *sKsAdvertiserAccountReportData) SyncKsAccountReportData(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 500
		// 记录已处理的账户
		processedKey := fmt.Sprintf("PLAT:SYNC:KS:ACCOUNT:REPORT:DATA:%s", statDate)
		for {
			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				g.Log().Info(ctx, "SyncKsAccountReportData任务被取消或超时")
				return
			default:
				// 继续执行
			}
			// 获取账户列表
			advertiserList, _ := service.KsAdvertiserAccountInfo().GetAccountList(ctx, pageNo, pageSize)
			if len(advertiserList) == 0 {
				break
			}
			var statList = make([]*model.KsAdvertiserAccountReportDataAddReq, 0)
			for _, item := range advertiserList {
				// 检查该账户是否已处理
				isProcessed, _ := commonService.GetGoRedis().HExists(ctx, processedKey, gconv.String(item.AccountId)).Result()
				if isProcessed {
					continue
				}
				accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, item.AccountId)
				if accessToken == "" {
					// 标记为已处理（避免重复检查无token的账户）
					commonService.GetGoRedis().HSet(ctx, processedKey, item.AccountId, 1)
					g.Log().Infof(ctx, "同步账户报表数据失败, 广告主%v accessToken不存在", item.AccountId)
					continue
				}
				accountReportRes, err1 := ksApi.GetKSApiClient().AccountReportService.
					AccessToken(accessToken).
					SetReq(ksApi.AccountReportReq{
						AdvertiserID: item.AccountId,
						StartDate:    statDate,
						EndDate:      statDate,
						Page:         1,
						PageSize:     100,
					}).Do()
				commonService.GetGoRedis().HSet(ctx, processedKey, item.AccountId, 1)
				if err1 != nil {
					g.Log().Errorf(ctx, "同步账户报表数据失败: advertiserId: %v, err: %v", item.AccountId, err1)
					continue
				}
				if accountReportRes.Data == nil || len(accountReportRes.Data.Details) == 0 {
					continue
				}
				for _, accountDetail := range accountReportRes.Data.Details {
					var accountReport *model.KsAdvertiserAccountReportDataAddReq
					_ = gconv.Struct(accountDetail, &accountReport)
					accountReport.AdvertiserId = item.AccountId
					accountReport.AdScene2 = accountDetail.AdScene2
					statList = append(statList, accountReport)
					if len(statList) >= 100 {
						err = s.BatchAdd(ctx, statList)
						statList = slices.Delete(statList, 0, len(statList))
					}
				}
			}
			if len(statList) > 0 {
				err = s.BatchAdd(ctx, statList)
				statList = slices.Delete(statList, 0, len(statList))
			}
			pageNo++
		}
		commonService.GetGoRedis().Del(ctx, processedKey)
	})
	return
}
