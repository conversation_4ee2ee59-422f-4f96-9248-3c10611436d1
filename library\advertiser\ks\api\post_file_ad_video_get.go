package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

///rest/openapi/v1/file/ad/video/get

// FileAdVideoGetService 上传视频接口 v2
type FileAdVideoGetService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *FileAdVideoGetReq
}

type FileAdVideoGetReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主id
	//photo_ids
	PhotoIds []string `json:"photo_ids"`
}

type FileAdVideoGetResp struct {
	Code    int               `json:"code"`    // 状态码
	Message string            `json:"message"` // 响应消息
	Data    []*FileAdVideoGet `json:"data"`    // 优化目标列表视图
}

type FileAdVideoGet struct {
	PhotoId        string `json:"photo_id"`
	PhotoName      string `json:"photo_name"`
	PhotoDupStatus int    `json:"photo_dup_status"`
	Signature      string `json:"signature"`
	//AtlasPicIds           interface{} `json:"atlas_pic_ids"`
	NewStatus int `json:"new_status"` // `0：视频库删除，1：可用状态，2：转码中，3：审核中，11：转码失败，12：审核失败，13：已屏蔽，14：视频在客户端删除
	Source    int `json:"source"`
	//PhotoTagIdentifyItems interface{} `json:"photoTagIdentifyItems"`
	ShieldStatus  int `json:"shieldStatus"`
	Authorization int `json:"authorization"`
	Duration      int `json:"duration"`
	Height        int `json:"height"`
	//AtlasAudioUrl         interface{} `json:"atlas_audio_url"`
	CoverUrl           string `json:"cover_url"`
	PhotoWakeStatus    int    `json:"photo_wake_status"`
	PhotoUserId        int64  `json:"photoUserId"`
	CreateTime         string `json:"create_time"`
	AdPhotoValuateInfo struct {
		IsDupPhoto              bool   `json:"isDupPhoto"`
		RunningScore            int    `json:"runningScore"`
		QualityLabel            string `json:"qualityLabel"`
		OptimizationSuggestions string `json:"optimizationSuggestions"`
		HitTagCombination       int    `json:"hitTagCombination"`
		//IsDelayReview           interface{} `json:"isDelayReview"`
		QuotaMsg string `json:"quotaMsg"`
		SimLabel string `json:"simLabel"`
	} `json:"adPhotoValuateInfo"`
	Url              string `json:"url"`
	LowQualityStatus int    `json:"low_quality_status"`
	UploadTime       string `json:"upload_time"`
	//PhotoTag         interface{} `json:"photo_tag"`
	OuterLoopNative int `json:"outer_loop_native"`
	Width           int `json:"width"`
	//AtlasAudioBsKey  interface{} `json:"atlas_audio_bs_key"`
	Status int `json:"status"` // 视频状态`0：正常；1：删除
}

func (r *FileAdVideoGetService) SetCfg(cfg *Configuration) *FileAdVideoGetService {
	r.cfg = cfg
	return r
}

func (r *FileAdVideoGetService) SetReq(req FileAdVideoGetReq) *FileAdVideoGetService {
	r.Request = &req
	return r
}

func (r *FileAdVideoGetService) AccessToken(accessToken string) *FileAdVideoGetService {
	r.token = accessToken
	return r
}

func (r *FileAdVideoGetService) Do() (data *FileAdVideoGetResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/file/ad/video/get"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&FileAdVideoGetResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(FileAdVideoGetResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/file/ad/video/get解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
