// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/logic/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/tiger1103/gfast/v3/task"
	"math"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxMaterial(New())
}

func New() service.IAdxMaterial {
	return &sAdxMaterial{}
}

type sAdxMaterial struct{}

func (s *sAdxMaterial) List(ctx context.Context, req *model.AdxMaterialSearchReq) (listRes *model.AdxMaterialSearchRes, err error) {
	listRes = new(model.AdxMaterialSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m, orderBy := s.buildSqlModel(ctx, req)
		listRes.Total, err = m.CountColumn("DISTINCT m.material_id")
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		// 先获取到material_id
		var materialIdRes []*model.AdxMaterialRes
		err = m.Page(req.PageNum, req.PageSize).
			Fields("m.material_id as materialId").
			Group("m.material_id").
			Order(orderBy).Scan(&materialIdRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		var materialIds []uint64
		for _, v := range materialIdRes {
			materialIds = append(materialIds, v.MaterialId)
		}
		if len(materialIds) == 0 {
			return
		}
		var res []*model.AdxMaterialRes
		err = m.WhereIn("m.material_id", materialIds).
			Fields("m.material_id as materialId").
			Fields("ANY_VALUE(m.ad_type) as adTypes").
			Fields("ANY_VALUE(m.comment_num) as commentNum").
			Fields("ANY_VALUE(m.create_month) as createMonth").
			Fields("ANY_VALUE(m.download_num) as downloadNum").
			Fields("ANY_VALUE(m.duration_millis) as durationMillis").
			Fields("ANY_VALUE(m.exposure_num) as exposureNum").
			Fields("ANY_VALUE(m.forward_num) as forwardNum").
			Fields("ANY_VALUE(m.like_num) as likeNum").
			Fields("ANY_VALUE(m.material_height) as materialHeight").
			Fields("ANY_VALUE(m.material_type) as materialType").
			Fields("ANY_VALUE(m.material_width) as materialWidth").
			Fields("ANY_VALUE(m.pic_list) as picList").
			Fields("ANY_VALUE(m.play_num) as playNum").
			Fields("ANY_VALUE(m.video_list) as videoList").
			Fields("ANY_VALUE(p.product_name) as productName").
			Fields("ANY_VALUE(p.icon) as productIcon").
			Fields("ANY_VALUE(pu.publisher_name) as publisherName").
			Fields("ANY_VALUE(pl.playlet_id) as playletId").
			Fields("ANY_VALUE(pl.playlet_name) as playletName").
			Fields("ANY_VALUE(pl.description) as description").
			Fields("ANY_VALUE(c.media_id) as mediaId").
			FieldCount("c.creative_id", "creativeNum").
			FieldMin("c.first_seen", "firstSeen").
			FieldMax("c.last_seen", "lastSeen").
			Fields("ANY_VALUE(c.title1) as title1").
			Fields("ANY_VALUE(c.talent_name) as talentName").
			Fields("ANY_VALUE(c.mobile_type) as mobileType").
			Group("m.material_id").
			Order(orderBy).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxMaterialRes, len(res))
		// 查询媒体名称 logo
		var mediaIds []int
		for _, v := range res {
			mediaIds = append(mediaIds, v.MediaId)
		}
		adxMedias, _ := service.AdxMedia().GetByIds(ctx, mediaIds)
		for k, v := range res {
			v.DeliveryDays = libUtils.GetBetweenDays(v.FirstSeen, v.LastSeen)
			for _, media := range adxMedias {
				if uint(v.MediaId) == media.Id {
					v.MediaName = media.MediaName
					v.MediaLogoUrl = media.LogoUrl
					break
				}
			}
			listRes.List[k] = v
		}
	})
	return
}

func (s *sAdxMaterial) buildSqlModel(ctx context.Context, req *model.AdxMaterialSearchReq) (m *gdb.Model, orderBy string) {
	queryParams := s.buildQueryParams(ctx, req)
	m = dao.AdxMaterialAnalytic.Ctx(ctx).As("m").
		LeftJoin("adx_playlet", "pl", "pl.playlet_id = m.playlet_id").
		LeftJoin("adx_creative", "c", "c.material_id = m.material_id").
		LeftJoin("adx_product", "p", "p.id = c.product_id").
		LeftJoin("adx_publisher", "pu", "pu.id = c.publisher_id").
		WhereGT("m.playlet_id", 0)
	if req.SearchType == model.SearchTypeAll {
		if req.PreciseSearch == model.PreciseSearchNo {
			m = m.Where("p.id in (?) or pl.playlet_id in (?) or pu.id in (?) or c.title1 like ? or c.talent_name like ?",
				queryParams.ProductIds, queryParams.PlayletIds, queryParams.PublisherIds, "%"+req.SearchKey+"%", "%"+req.SearchKey+"%")
		} else if req.PreciseSearch == model.PreciseSearchYes {
			m = m.Where("p.id in (?) or pl.playlet_id in (?) or pu.id in (?) or c.title1 = ? or c.talent_name = ?",
				queryParams.ProductIds, queryParams.PlayletIds, queryParams.PublisherIds, req.SearchKey, req.SearchKey)
		}
	} else if req.SearchType == model.SearchTypeProductName {
		m = m.Where("p.id in (?)", queryParams.ProductIds)
		if queryParams.MinProductCreativeId > 0 {
			m = m.Where("c.creative_id > ?", queryParams.MinProductCreativeId)
		}
	} else if req.SearchType == model.SearchTypePlayletName {
		m = m.Where("pl.playlet_id in (?)", queryParams.PlayletIds)
		if queryParams.MinMaterialId > 0 {
			m = m.Where("m.material_id > ?", queryParams.MinMaterialId)
		}
	} else if req.SearchType == model.SearchTypePublisherName {
		m = m.Where("pu.id in (?)", queryParams.PublisherIds)
		if queryParams.MinPublisherCreativeId > 0 {
			m = m.Where("c.creative_id > ?", queryParams.MinPublisherCreativeId)
		}
	} else if req.SearchType == model.SearchTypeTitle {
		if req.PreciseSearch == model.PreciseSearchNo {
			m = m.Where("c.title1 like ? ", "%"+req.SearchKey+"%")
		} else if req.PreciseSearch == model.PreciseSearchYes {
			m = m.Where("c.title1 = ?", req.SearchKey)
		}
	} else if req.SearchType == model.SearchTypeTalentName {
		if req.PreciseSearch == model.PreciseSearchNo {
			m = m.Where("c.talent_name like? ", "%"+req.SearchKey+"%")
		} else if req.PreciseSearch == model.PreciseSearchYes {
			m = m.Where("c.talent_name = ?", req.SearchKey)
		}
	}
	if len(req.AdTypes) > 0 {
		var condition string
		for index, adType := range req.AdTypes {
			if index == 0 {
				condition += fmt.Sprintf("JSON_CONTAINS(m.ad_type,cast('[\"%s\"]' AS json))", adType)
			} else {
				condition += " OR " + fmt.Sprintf("JSON_CONTAINS(m.ad_type,cast('[\"%s\"]' AS json))", adType)
			}
		}
		m = m.Where(condition)
	}
	if len(req.MediaTypes) > 0 {
		m = m.WhereIn("c.media_id", req.MediaTypes)
	}
	if len(req.MobileTypes) > 0 {
		m = m.WhereIn("c.mobile_type", req.MobileTypes)
	}
	if req.StartTime != "" {
		m = m.WhereGTE("c.first_seen", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.WhereLTE("c.first_seen", req.EndTime)
	}
	if req.PlayletId != "" {
		m = m.Where("m.playlet_id = ?", gconv.Int64(req.PlayletId))
	}
	if req.MaterialType > 0 {
		m = m.Where("m.material_type = ?", req.MaterialType)
	}
	if req.SortType == model.SortTypeRecentAppearance {
		orderBy = "MAX(c.last_seen) desc"
	} else if req.SortType == model.SortTypeMostExposure {
		orderBy = "ANY_VALUE(m.exposure_num) desc"
	} else if req.SortType == model.SortTypeMostConversion {
		orderBy = "ANY_VALUE(m.download_num) desc"
	} else if req.SortType == model.SortTypeMostLike {
		orderBy = "ANY_VALUE(m.like_num) desc"
	} else if req.SortType == model.SortTypeMostComment {
		orderBy = "ANY_VALUE(m.comment_num) desc"
	} else if req.SortType == model.SortTypeMostForward {
		orderBy = "ANY_VALUE(m.forward_num) desc"
	} else if req.SortType == model.SortTypeMostPlay {
		orderBy = "ANY_VALUE(m.play_num) desc"
	} else {
		orderBy = "MAX(c.first_seen) desc"
	}
	orderBy = orderBy + ", m.material_id desc"
	return
}

func (s *sAdxMaterial) buildQueryParams(ctx context.Context, req *model.AdxMaterialSearchReq) (res *model.AdxMaterialQueryParamRes) {
	if req.SearchKey == "" {
		return
	}
	res = new(model.AdxMaterialQueryParamRes)
	var adxProducts []*model.AdxProductInfoRes
	var adxPlaylets []*model.AdxPlayletInfoRes
	var adxPublishers []*model.AdxPublisherInfoRes
	if req.SearchType == model.SearchTypeAll {
		var likeQuery bool
		if req.PreciseSearch == model.PreciseSearchNo {
			likeQuery = true
		}
		adxProducts, _ = service.AdxProduct().GetByName(ctx, req.SearchKey, likeQuery)
		adxPlaylets, _ = service.AdxPlaylet().GetByName(ctx, req.SearchKey, likeQuery)
		adxPublishers, _ = service.AdxPublisher().GetByName(ctx, req.SearchKey, likeQuery)
	} else if req.SearchType == model.SearchTypeProductName {
		var likeQuery bool
		if req.PreciseSearch == model.PreciseSearchNo {
			likeQuery = true
		}
		adxProducts, _ = service.AdxProduct().GetByName(ctx, req.SearchKey, likeQuery)
	} else if req.SearchType == model.SearchTypePlayletName {
		var likeQuery bool
		if req.PreciseSearch == model.PreciseSearchNo {
			likeQuery = true
		}
		adxPlaylets, _ = service.AdxPlaylet().GetByName(ctx, req.SearchKey, likeQuery)
	} else if req.SearchType == model.SearchTypePublisherName {
		var likeQuery bool
		if req.PreciseSearch == model.PreciseSearchNo {
			likeQuery = true
		}
		adxPublishers, _ = service.AdxPublisher().GetByName(ctx, req.SearchKey, likeQuery)
	}
	for _, v := range adxProducts {
		res.ProductIds = append(res.ProductIds, gconv.Int64(v.Id))
	}
	for _, v := range adxPlaylets {
		res.PlayletIds = append(res.PlayletIds, gconv.Int64(v.PlayletId))
	}
	for _, v := range adxPublishers {
		res.PublisherIds = append(res.PublisherIds, gconv.Int64(v.Id))
	}
	if len(res.ProductIds) == 0 {
		res.ProductIds = append(res.ProductIds, -100)
		res.MinProductCreativeId = math.MaxInt64
	} else {
		minAdxCreatvie, _ := service.AdxCreative().GetMinByProductIds(ctx, res.ProductIds)
		if minAdxCreatvie != nil {
			res.MinProductCreativeId = minAdxCreatvie.CreativeId
		}
	}
	if len(res.PlayletIds) == 0 {
		res.MinMaterialId = math.MaxInt64
		res.PlayletIds = append(res.PlayletIds, -100)
	} else {
		minAdxMaterial, _ := service.AdxMaterial().GetMinByPlayletIds(ctx, res.PlayletIds)
		if minAdxMaterial != nil {
			res.MinMaterialId = minAdxMaterial.MaterialId
		}
	}
	if len(res.PublisherIds) == 0 {
		res.PublisherIds = append(res.PublisherIds, -100)
		res.MinPublisherCreativeId = math.MaxInt64
	} else {
		minAdxCreatvie, _ := service.AdxCreative().GetMinByPublisherIds(ctx, res.PublisherIds)
		if minAdxCreatvie != nil {
			res.MinPublisherCreativeId = minAdxCreatvie.CreativeId
		}
	}
	return
}

func (s *sAdxMaterial) GetExportData(ctx context.Context, req *model.AdxMaterialSearchReq) (listRes []*model.AdxMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

	})
	return
}

func (s *sAdxMaterial) GetMinByPlayletIds(ctx context.Context, playletIds []int64) (res *model.AdxMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxMaterial.Ctx(ctx).WithAll().
			FieldMin("material_id", "materialId").
			WhereIn(dao.AdxMaterial.Columns().PlayletId, playletIds).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxMaterial) GetByMaterialId(ctx context.Context, materialId uint64) (res *model.AdxMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxMaterial.Ctx(ctx).WithAll().Where(dao.AdxMaterial.Columns().MaterialId, materialId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxMaterial) GetByMaterialIds(ctx context.Context, MaterialIds []uint64) (res []*model.AdxMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxMaterial.Ctx(ctx).WhereIn(dao.AdxMaterial.Columns().MaterialId, MaterialIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxMaterial) Add(ctx context.Context, req *model.AdxMaterialAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMaterial.Ctx(ctx).Insert(do.AdxMaterial{
			MaterialId:     req.MaterialId,
			AdType:         req.AdType,
			CommentNum:     req.CommentNum,
			CreateMonth:    req.CreateMonth,
			DownloadNum:    req.DownloadNum,
			DurationMillis: req.DurationMillis,
			ExposureNum:    req.ExposureNum,
			ForwardNum:     req.ForwardNum,
			LikeNum:        req.LikeNum,
			MaterialHeight: req.MaterialHeight,
			MaterialType:   req.MaterialType,
			MaterialWidth:  req.MaterialWidth,
			PicList:        req.PicList,
			PlayNum:        req.PlayNum,
			VideoList:      req.VideoList,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxMaterial) BatchAdd(ctx context.Context, batchReq []*model.AdxMaterialAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdxMaterial, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdxMaterial{
				MaterialId:     v.MaterialId,
				AdType:         v.AdType,
				CommentNum:     v.CommentNum,
				CreateMonth:    v.CreateMonth,
				DownloadNum:    v.DownloadNum,
				DurationMillis: v.DurationMillis,
				ExposureNum:    v.ExposureNum,
				ForwardNum:     v.ForwardNum,
				LikeNum:        v.LikeNum,
				MaterialHeight: v.MaterialHeight,
				MaterialType:   v.MaterialType,
				MaterialWidth:  v.MaterialWidth,
				PicList:        v.PicList,
				PlayNum:        v.PlayNum,
				PlayletId:      v.PlayletId,
				VideoList:      v.VideoList,
			}
		}
		_, err = dao.AdxMaterial.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxMaterial) Edit(ctx context.Context, req *model.AdxMaterialEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMaterial.Ctx(ctx).WherePri(req.MaterialId).Update(do.AdxMaterial{
			AdType:         req.AdType,
			CommentNum:     req.CommentNum,
			CreateMonth:    req.CreateMonth,
			DownloadNum:    req.DownloadNum,
			DurationMillis: req.DurationMillis,
			ExposureNum:    req.ExposureNum,
			ForwardNum:     req.ForwardNum,
			LikeNum:        req.LikeNum,
			MaterialHeight: req.MaterialHeight,
			MaterialType:   req.MaterialType,
			MaterialWidth:  req.MaterialWidth,
			PicList:        req.PicList,
			PlayNum:        req.PlayNum,
			PlayletId:      req.PlayletId,
			VideoList:      req.VideoList,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxMaterial) Delete(ctx context.Context, materialIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMaterial.Ctx(ctx).Delete(dao.AdxMaterial.Columns().MaterialId+" in (?)", materialIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdxMaterial) RunSyncAdxMaterialTask(ctx context.Context, req *model.AdxMaterialSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncAdxMaterial(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdxMaterial) SyncAdxMaterialTask(ctx context.Context) {
	err := task.ExecuteWithTimeout(
		ctx,
		commonConsts.PlatAdxMaterialLock,
		59*time.Minute,
		"同步昨天ADX素材",
		"SyncAdxMaterialTask",
		func(ctx context.Context) error {
			yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
			return s.SyncAdxMaterial(ctx, yesterday)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxMaterial) SyncTodayAdxMaterialTask(ctx context.Context) {
	err := task.ExecuteWithTimeout(
		ctx,
		commonConsts.PlatAdxMaterialTodayLock,
		(15*60-10)*time.Second,
		"同步当天ADX素材",
		"SyncTodayAdxMaterialTask",
		func(ctx context.Context) error {
			today := gtime.Now().Format("Y-m-d")
			return s.SyncAdxMaterial(ctx, today)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxMaterial) SyncAdxMaterial(ctx context.Context, updateDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var id int64 = 0
		var pageSize = 500
		var pullType = 2
		if updateDate == "" {
			pullType = 1
		}
		var pageIdKey string
		var ttlInSeconds int64
		if pullType == 1 {
			pageIdKey = commonConsts.PlatAdxMaterialPageId
		} else {
			pageIdKey = fmt.Sprintf("%s%s", commonConsts.PlatAdxMaterialDayPageId, updateDate)
			ttlInSeconds = commonConsts.PlatAdxPageIdTtlSeconds
		}
		data, _ := commonService.RedisCache().Get(ctx, pageIdKey)
		if !data.IsNil() {
			id = data.Int64()
		}
		maxRetries := 5
		retryCount := 0
		waitTime := 10 * time.Second
		for {
			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				g.Log().Info(ctx, "SyncAdxMaterial任务被取消或超时")
				return
			default:
				// 继续执行
			}
			materialRes, err1 := api.GetAdxApiClient().MaterialService.SetReq(api.MaterialRequest{
				Id:         id,
				PageSize:   pageSize,
				PullType:   pullType,
				UpdateDate: updateDate,
			}).Do()
			if err1 != nil && err1.Error() == "Error limiting" {
				retryCount++
				if retryCount > maxRetries {
					g.Log().Error(ctx, "SyncAdxMaterial Maximum retries reached for Error limiting")
					break
				}
				g.Log().Infof(ctx, "SyncAdxMaterial Rate limit hit, retrying in %v (attempt %d/%d)", waitTime, retryCount, maxRetries)
				time.Sleep(waitTime)
				continue
			}
			retryCount = 0
			if err1 != nil {
				g.Log().Errorf(ctx, "SyncAdxMaterial异常：%v", err1)
				break
			}
			var batchReq []*model.AdxMaterialAddReq
			for _, v := range materialRes.Content {
				var adType = v.AdType
				var picList = v.PicList
				var videoList = v.VideoList
				if adType == nil {
					adType = []string{}
				}
				if picList == nil {
					picList = []string{}
				}
				if videoList == nil {
					videoList = []string{}
				}
				batchReq = append(batchReq, &model.AdxMaterialAddReq{
					MaterialId:     uint64(v.MaterialId),
					AdType:         adType,
					CommentNum:     v.CommentNum,
					CreateMonth:    v.CreateMonth,
					DownloadNum:    v.DownloadNum,
					DurationMillis: v.DurationMillis,
					ExposureNum:    v.ExposureNum,
					ForwardNum:     v.ForwardNum,
					LikeNum:        v.LikeNum,
					MaterialHeight: uint(v.MaterialHeight),
					MaterialType:   uint(v.MaterialType),
					MaterialWidth:  uint(v.MaterialWidth),
					PicList:        picList,
					PlayNum:        v.PlayNum,
					PlayletId:      v.PlayletId,
					VideoList:      videoList,
				})
			}
			if len(batchReq) > 0 {
				err = s.BatchAdd(ctx, batchReq)
				if err != nil {
					g.Log().Error(ctx, err)
					break
				}
			}
			if materialRes.Page.PageId == id {
				break
			}
			id = materialRes.Page.PageId
			if ttlInSeconds > 0 {
				_ = commonService.RedisCache().SetEX(ctx, pageIdKey, id, ttlInSeconds)
			} else {
				_, _ = commonService.RedisCache().Set(ctx, pageIdKey, id)
			}
		}
	})
	return
}
