// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-23 15:51:08
// 生成路径: internal/app/ad/model/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialUploadInfoRes is the golang structure for table ad_material_upload.
type AdMaterialUploadInfoRes struct {
	gmeta.Meta   `orm:"table:ad_material_upload"`
	Id           int         `orm:"id,primary" json:"id" dc:"自增ID"`                                            // 自增ID
	MediaType    int         `orm:"media_type" json:"mediaType" dc:"媒体类型：图片或视频 1 图片 2视频"`                      // 媒体类型：图片或视频 1 图片 2视频
	AdMaterialId int         `orm:"ad_material_id" json:"adMaterialId" dc:"素材id 关联ad_material 表格 material_id"` // 素材id 关联ad_material 表格 material_id
	AdvertiserId string      `orm:"advertiser_id" json:"advertiserId" dc:"广告账户id"`                             // 广告账户id
	MediaId      string      `orm:"media_id" json:"mediaId" dc:"图片/视频ID"`                                      // 图片/视频ID
	MaterialName string      `orm:"material_name" json:"materialName" dc:"素材名"`                                // 素材名
	Size         int64       `orm:"size" json:"size" dc:"媒体文件大小"`                                              // 媒体文件大小
	Width        int         `orm:"width" json:"width" dc:"宽度"`                                                // 宽度
	Height       int         `orm:"height" json:"height" dc:"高度"`                                              // 高度
	Url          string      `orm:"url" json:"url" dc:"图片预览地址或视频地址"`                                           // 图片预览地址或视频地址
	Format       string      `orm:"format" json:"format" dc:"图片格式"`                                            // 图片格式
	Signature    string      `orm:"signature" json:"signature" dc:"图片md5 或视频md5"`                              // 图片md5 或视频md5
	AdType       int         `orm:"ad_type" json:"adType" dc:"广告类型 1:巨量  2 快手"`
	MaterialId   string      `orm:"material_id" json:"materialId" dc:"素材id，即多合一报表中的素材id，唯一对应一个素材id"` // 素材id，即多合一报表中的素材id，唯一对应一个素材id
	Duration     float64     `orm:"duration" json:"duration" dc:"视频时长，如果是视频，则填充此字段"`                 // 视频时长，如果是视频，则填充此字段
	TaskId       string      `orm:"task_id" json:"taskId" dc:"任务id"`
	NewStatus    int         `orm:"new_status" json:"newStatus"  `
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt    *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
}

type AdMaterialUploadListRes struct {
	Id           int         `json:"id" dc:"自增ID"`
	MediaType    int         `json:"mediaType" dc:"媒体类型：图片或视频 1 图片 2视频"`
	AdMaterialId int         `json:"adMaterialId" dc:"素材id 关联ad_material 表格 material_id"`
	AdvertiserId string      `json:"advertiserId" dc:"广告账户id"`
	MediaId      string      `json:"mediaId" dc:"图片/视频ID"`
	Size         int64       `json:"size" dc:"媒体文件大小"`
	Width        int         `json:"width" dc:"宽度"`
	Height       int         `json:"height" dc:"高度"`
	Url          string      `json:"url" dc:"图片预览地址或视频地址"`
	Format       string      `json:"format" dc:"图片格式"`
	AdType       int         `json:"adType" dc:"广告类型 1:巨量  2 快手"`
	Signature    string      `json:"signature" dc:"图片md5 或视频md5"`
	MaterialId   string      `json:"materialId" dc:"素材id，即多合一报表中的素材id，唯一对应一个素材id"`
	Duration     float64     `json:"duration" dc:"视频时长，如果是视频，则填充此字段"`
	CreatedAt    *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdMaterialUploadSearchReq 分页请求参数
type AdMaterialUploadSearchReq struct {
	comModel.PageReq
	Id           string `p:"id" dc:"自增ID"`                                                                                                       //自增ID
	MediaType    string `p:"mediaType" v:"mediaType@integer#媒体类型：图片或视频 1 图片 2视频需为整数" dc:"媒体类型：图片或视频 1 图片 2视频"`                                   //媒体类型：图片或视频 1 图片 2视频
	AdMaterialId string `p:"adMaterialId" v:"adMaterialId@integer#素材id 关联ad_material 表格 material_id需为整数" dc:"素材id 关联ad_material 表格 material_id"` //素材id 关联ad_material 表格 material_id
	AdvertiserId string `p:"advertiserId" dc:"广告账户id"`                                                                                           //广告账户id
	MediaId      string `p:"mediaId" dc:"图片/视频ID"`                                                                                               //图片/视频ID
	Size         string `p:"size" v:"size@integer#媒体文件大小需为整数" dc:"媒体文件大小"`                                                                       //媒体文件大小
	Width        string `p:"width" v:"width@integer#宽度需为整数" dc:"宽度"`                                                                             //宽度
	Height       string `p:"height" v:"height@integer#高度需为整数" dc:"高度"`                                                                           //高度
	Url          string `p:"url" dc:"图片预览地址或视频地址"`                                                                                               //图片预览地址或视频地址
	Format       string `p:"format" dc:"图片格式"`                                                                                                   //图片格式
	Signature    string `p:"signature" dc:"图片md5 或视频md5"`                                                                                        //图片md5 或视频md5
	MaterialId   string `p:"materialId" v:"materialId@integer#素材id，即多合一报表中的素材id，唯一对应一个素材id需为整数" dc:"素材id，即多合一报表中的素材id，唯一对应一个素材id"`               //素材id，即多合一报表中的素材id，唯一对应一个素材id
	Duration     string `p:"duration" v:"duration@float#视频时长，如果是视频，则填充此字段需为浮点数" dc:"视频时长，如果是视频，则填充此字段"`                                          //视频时长，如果是视频，则填充此字段
	CreatedAt    string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                             //创建时间
}

type SyncMediaMaterialToLocalReq struct {
	AdvertiserId string `p:"advertiserId" v:"required#广告账户id不能为空" dc:"广告账户id"`
	FileId       int    `p:"fileId" dc:"id"`  // 文件夹id
	AlbumId      int    `p:"albumId" dc:"id"` // 专辑id
	Ids          []int  `p:"ids" v:"required#请选择要同步的素材" dc:"请选择要同步的素材"`
}

// AdMaterialUploadSearchRes 列表返回结果
type AdMaterialUploadSearchRes struct {
	comModel.ListRes
	List []*AdMaterialUploadListRes `json:"list"`
}

// AdMaterialUploadAddReq 添加操作请求参数
type AdMaterialUploadAddReq struct {
	MediaType    int     `p:"mediaType" v:"required#媒体类型：图片或视频 1 图片 2视频不能为空" dc:"媒体类型：图片或视频 1 图片 2视频"`
	AdMaterialId int     `p:"adMaterialId" v:"required#素材id 关联ad_material 表格 material_id不能为空" dc:"素材id 关联ad_material 表格 material_id"`
	AdvertiserId string  `p:"advertiserId" v:"required#广告账户id不能为空" dc:"广告账户id"`
	MediaId      string  `p:"mediaId" v:"required#图片/视频ID不能为空" dc:"图片/视频ID"`
	Size         int64   `p:"size" v:"required#媒体文件大小不能为空" dc:"媒体文件大小"`
	Width        int     `p:"width" v:"required#宽度不能为空" dc:"宽度"`
	Height       int     `p:"height" v:"required#高度不能为空" dc:"高度"`
	Url          string  `p:"url"  dc:"图片预览地址或视频地址"`
	AdType       int     `p:"adType"   dc:"广告类型 1:巨量  2 快手"`
	Format       string  `p:"format"  dc:"图片格式"`
	Signature    string  `p:"signature"  dc:"图片md5 或视频md5"`
	MaterialId   string  `p:"materialId" v:"required#素材id，即多合一报表中的素材id，唯一对应一个素材id不能为空" dc:"素材id，即多合一报表中的素材id，唯一对应一个素材id"`
	Duration     float64 `p:"duration"  dc:"视频时长，如果是视频，则填充此字段"`
}

// AdMaterialUploadEditReq 修改操作请求参数
type AdMaterialUploadEditReq struct {
	Id           int     `p:"id" v:"required#主键ID不能为空" dc:"自增ID"`
	MediaType    int     `p:"mediaType" v:"required#媒体类型：图片或视频 1 图片 2视频不能为空" dc:"媒体类型：图片或视频 1 图片 2视频"`
	AdMaterialId int     `p:"adMaterialId" v:"required#素材id 关联ad_material 表格 material_id不能为空" dc:"素材id 关联ad_material 表格 material_id"`
	AdvertiserId string  `p:"advertiserId" v:"required#广告账户id不能为空" dc:"广告账户id"`
	MediaId      string  `p:"mediaId" v:"required#图片/视频ID不能为空" dc:"图片/视频ID"`
	Size         int64   `p:"size" v:"required#媒体文件大小不能为空" dc:"媒体文件大小"`
	Width        int     `p:"width" v:"required#宽度不能为空" dc:"宽度"`
	Height       int     `p:"height" v:"required#高度不能为空" dc:"高度"`
	Url          string  `p:"url"  dc:"图片预览地址或视频地址"`
	AdType       int     `p:"adType"  dc:"广告类型 1:巨量  2 快手"`
	Format       string  `p:"format"  dc:"图片格式"`
	Signature    string  `p:"signature"  dc:"图片md5 或视频md5"`
	MaterialId   string  `p:"materialId" v:"required#素材id，即多合一报表中的素材id，唯一对应一个素材id不能为空" dc:"素材id，即多合一报表中的素材id，唯一对应一个素材id"`
	Duration     float64 `p:"duration"  dc:"视频时长，如果是视频，则填充此字段"`
}
