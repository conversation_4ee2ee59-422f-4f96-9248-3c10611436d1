// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:52:28
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	adxModel "github.com/tiger1103/gfast/v3/internal/app/adx/model"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyMaterial interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyMaterialSearchReq) (res *model.KsAdvertiserStrategyMaterialSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserStrategyMaterialInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyMaterialInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyMaterialAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyMaterialEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
	UpLoad(ctx context.Context, aId string, fileId int, taskId string) (result *model.UpLoadVideoRes, err error)
	UpLoadAdx(ctx context.Context, aId string, fileId int, file adxModel.AdxMaterialInfoRes, taskId string) (result *model.UpLoadVideoRes, err error)
	UpLoadByUrl(ctx context.Context, aId, fileUrl string, isVideo bool, taskId string) (result *model.UpLoadVideoRes, err error)
}

var localKsAdvertiserStrategyMaterial IKsAdvertiserStrategyMaterial

func KsAdvertiserStrategyMaterial() IKsAdvertiserStrategyMaterial {
	if localKsAdvertiserStrategyMaterial == nil {
		panic("implement not found for interface IKsAdvertiserStrategyMaterial, forgot register?")
	}
	return localKsAdvertiserStrategyMaterial
}

func RegisterKsAdvertiserStrategyMaterial(i IKsAdvertiserStrategyMaterial) {
	localKsAdvertiserStrategyMaterial = i
}
