package toutiao

import (
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
)

// APIClient 支持的api
type APIClient struct {
	Cfg                            *Configuration
	Oauth2RefreshTokenApi          *Oauth2RefreshTokenApiService
	MajordomoAdvertiserSelectV2Api *MajordomoAdvertiserSelectV2Service
	ReportCustomGetV30Api          *ReportCustomGetV30ApiService
	Oauth2AccessTokenApiService    *Oauth2AccessTokenApiService
	*GetOauth2AppAccessTokenApiService
	Oauth2AdvertiserGetApiService              *Oauth2AdvertiserGetApiService
	AdvertiserInfoV2ApiService                 *AdvertiserInfoV2ApiService
	AdvertiserPublicInfoV2ApiService           *AdvertiserPublicInfoV2ApiService
	UserInfoV2ApiService                       *UserInfoV2ApiService
	CustomerCenterAdvertiserListV2ApiService   *CustomerCenterAdvertiserListV2ApiService
	AccountFundGetV3ApiService                 *AccountFundGetV3ApiService
	BusinessPlatformCompanyInfoGetV3ApiService *BusinessPlatformCompanyInfoGetV3ApiService
	AdvertiserFundDailyStatV2ApiService        *AdvertiserFundDailyStatV2ApiService
	//ReportAdvertiserGetV2ApiService                 *ReportAdvertiserGetV2ApiService
	AdvertiserBudgetGetV2ApiService       *AdvertiserBudgetGetV2ApiService
	AdvertiserBudgetUpdateV2ApiService    *AdvertiserBudgetUpdateV2ApiService
	ProjectListV3ApiService               *ProjectListV3ApiService
	ProjectStatusUpdateV3ApiService       *ProjectStatusUpdateV3ApiService
	ProjectRoigoalUpdateV3ApiService      *ProjectRoigoalUpdateV3ApiService
	ProjectBudgetUpdateV3ApiService       *ProjectBudgetUpdateV3ApiService
	ProjectWeekScheduleUpdateV3ApiService *ProjectWeekScheduleUpdateV3ApiService
	ProjectCreateV3ApiService             *ProjectCreateV3ApiService
	ProjectUpdateV3ApiService             *ProjectUpdateV3ApiService
	PromotionListV3ApiService             *PromotionListV3ApiService
	PromotionStatusUpdateV3ApiService     *PromotionStatusUpdateV3ApiService
	PromotionBudgetUpdateV3ApiService     *PromotionBudgetUpdateV3ApiService
	PromotionBidUpdateV3ApiService        *PromotionBidUpdateV3ApiService
	*PromotionDeleteV3ApiService
	*PromotionUpdateV30ApiService
	*ToolsPromotionRaiseSetV30ApiService
	ToolsWechatAppletCreateV3ApiService             *ToolsWechatAppletCreateV3ApiService
	ToolsWechatAppletListV3ApiService               *ToolsWechatAppletListV3ApiService
	ToolsMicroAppCreateV3ApiService                 *ToolsMicroAppCreateV3ApiService
	ToolsMicroAppListV3ApiService                   *ToolsMicroAppListV3ApiService
	ToolsMicroAppUpdateV3ApiService                 *ToolsMicroAppUpdateV3ApiService
	ToolsAssetLinkListV3ApiService                  *ToolsAssetLinkListV3ApiService
	DmpCustomAudienceSelectV2ApiService             *DmpCustomAudienceSelectV2ApiService
	DmpCustomAudienceReadV2ApiService               *DmpCustomAudienceReadV2ApiService
	DmpCustomAudiencePublishV2ApiService            *DmpCustomAudiencePublishV2ApiService
	DmpCustomAudiencePushV2ApiService               *DmpCustomAudiencePushV2ApiService
	DmpCustomAudienceDeleteV2ApiService             *DmpCustomAudienceDeleteV2ApiService
	ToolsEventAllAssetsListGetV2ApiService          *ToolsEventAllAssetsListGetV2ApiService
	ToolsEventAllAssetsDetailGetV2ApiService        *ToolsEventAllAssetsDetailGetV2ApiService
	EventManagerEventConfigsGetV2ApiService         *EventManagerEventConfigsGetV2ApiService
	ToolsSiteGetV2ApiService                        *ToolsSiteGetV2ApiService
	ToolsSitePreviewV2ApiService                    *ToolsSitePreviewV2ApiService
	ToolsSiteUpdateStatusV2ApiService               *ToolsSiteUpdateStatusV2ApiService
	ToolsSiteHandselV2ApiService                    *ToolsSiteHandselV2ApiService
	AudiencePackageCreateV2ApiService               *AudiencePackageCreateV2ApiService
	AudiencePackageDeleteV2ApiService               *AudiencePackageDeleteV2ApiService
	AudiencePackageGetV3ApiService                  *AudiencePackageGetV3ApiService
	ToolsEstimateAudienceV2ApiService               *ToolsEstimateAudienceV2ApiService
	ToolsAdminInfoGetV2ApiService                   *ToolsAdminInfoGetV2ApiService
	ToolsInterestActionActionCategoryV2ApiService   *ToolsInterestActionActionCategoryV2ApiService
	ToolsInterestActionInterestCategoryV2ApiService *ToolsInterestActionInterestCategoryV2ApiService
	ToolsInterestActionActionKeywordV2ApiService    *ToolsInterestActionActionKeywordV2ApiService
	ToolsInterestActionKeywordSuggestV2ApiService   *ToolsInterestActionKeywordSuggestV2ApiService
	ToolsInterestActionInterestKeywordV2ApiService  *ToolsInterestActionInterestKeywordV2ApiService
	ToolsInterestActionActionId2wordV2ApiService    *ToolsInterestActionActionId2wordV2ApiService
	ToolsAwemeMultiLevelCategoryGetV2ApiService     *ToolsAwemeMultiLevelCategoryGetV2ApiService
	ToolsAwemeCategoryTopAuthorGetV2ApiService      *ToolsAwemeCategoryTopAuthorGetV2ApiService
	ToolsAwemeInfoSearchGetV2ApiService             *ToolsAwemeInfoSearchGetV2ApiService
	ToolsAwemeSimilarAuthorSearchGetV2ApiService    *ToolsAwemeSimilarAuthorSearchGetV2ApiService
	ToolsQuotaGetV2ApiService                       *ToolsQuotaGetV2ApiService
	ToolsOrangeSiteGetV3ApiService                  *ToolsOrangeSiteGetV3ApiService
	FileImageAdV2ApiService                         *FileImageAdV2ApiService
	FileUploadTaskCreateV2ApiService                *FileUploadTaskCreateV2ApiService
	FileImageAdGetV2ApiService                      *FileImageAdGetV2ApiService
	FileVideoAdGetV2ApiService                      *FileVideoAdGetV2ApiService
	FileVideoAdV2ApiService                         *FileVideoAdV2ApiService
	FileVideoUploadTaskListV2ApiService             *FileVideoUploadTaskListV2ApiService
	FileMaterialBindV2ApiService                    *FileMaterialBindV2ApiService
	FileImageGetV2ApiService                        *FileImageGetV2ApiService
	FileVideoGetV2ApiService                        *FileVideoGetV2ApiService
	AdvertiserAvatarGetV2ApiService                 *AdvertiserAvatarGetV2ApiService
	AdvertiserAvatarSubmitV2ApiService              *AdvertiserAvatarSubmitV2ApiService
	PromotionCreateV30ApiService                    *PromotionCreateV30ApiService
	DpaProductAvailablesV2ApiService                *DpaProductAvailablesV2ApiService
	DpaDetailGetV2ApiService                        *DpaDetailGetV2ApiService
	DpaProductDetailGetV2ApiService                 *DpaProductDetailGetV2ApiService
	ToolsAwemeAuthListV2ApiService                  *ToolsAwemeAuthListV2ApiService
	DpaClueProductListV2ApiService                  *DpaClueProductListV2ApiService
	DpaClueProductDetailV2ApiService                *DpaClueProductDetailV2ApiService
	NativeAnchorCreateV30ApiService                 *NativeAnchorCreateV30ApiService
	NativeAnchorDeleteV30ApiService                 *NativeAnchorDeleteV30ApiService
	NativeAnchorGetDetailV30ApiService              *NativeAnchorGetDetailV30ApiService
	NativeAnchorGetV30ApiService                    *NativeAnchorGetV30ApiService
	NativeAnchorQrcodePreviewGetV30ApiService       *NativeAnchorQrcodePreviewGetV30ApiService
	NativeAnchorUpdateV30ApiService                 *NativeAnchorUpdateV30ApiService
	*AssetsCreativeComponentUpdateV2ApiService
	*AssetsCreativeComponentGetV2ApiService
	*AssetsCreativeComponentCreateV2ApiService
	*EventManagerOptimizedGoalGetV2V30ApiService
	*EventManagerDeepBidTypeGetV30ApiService
	*AgentInfoGetV2ApiService
	*AgentAdvertiserSelectV2ApiService
	*StarDemandOmGetChallengeItemsDataV2ApiService
	*StarDemandOmGetChallengeV2ApiService
	*StarDemandOmGetDemandListV2ApiService
	*EventManagerAssetsCreateV2ApiService
	*EventManagerEventsCreateV2ApiService
	*EventManagerAvailableEventsGetV2ApiService
	*AgentAdvertiserUpdateV2ApiService
	*ToolsSiteCreateV2ApiService
	*AdvertiserAvatarUploadV2ApiService

	*DpaAlbumStatusGetApiService
	*DpaAlbumCreateApiService
}

//func (c *Client) ReportAdvertiserGetV2Api() *ReportAdvertiserGetV2ApiService {
//	return c.ApiClient.ReportAdvertiserGetV2ApiService
//}

func (c *Client) AdvertiserFundDailyStatV2Api() *AdvertiserFundDailyStatV2ApiService {
	return c.ApiClient.AdvertiserFundDailyStatV2ApiService

}

func (c *Client) Oauth2RefreshTokenApi() *Oauth2RefreshTokenApiService {
	return c.ApiClient.Oauth2RefreshTokenApi
}

func (c *Client) MajordomoAdvertiserSelectV2Api() *MajordomoAdvertiserSelectV2Service {
	return c.ApiClient.MajordomoAdvertiserSelectV2Api
}

func (c *Client) ReportCustomGetV30Api() *ReportCustomGetV30ApiService {
	return c.ApiClient.ReportCustomGetV30Api
}

func (c *Client) Oauth2AccessTokenApi() *Oauth2AccessTokenApiService {
	return c.ApiClient.Oauth2AccessTokenApiService
}

func (c *Client) Oauth2AdvertiserGetApi() *Oauth2AdvertiserGetApiService {
	return c.ApiClient.Oauth2AdvertiserGetApiService
}

func (c *Client) AdvertiserInfoV2Api() *AdvertiserInfoV2ApiService {
	return c.ApiClient.AdvertiserInfoV2ApiService
}

func (c *Client) AdvertiserPublicInfoV2Api() *AdvertiserPublicInfoV2ApiService {
	return c.ApiClient.AdvertiserPublicInfoV2ApiService
}

func (c *Client) UserInfoV2Api() *UserInfoV2ApiService {
	return c.ApiClient.UserInfoV2ApiService
}

func (c *Client) CustomerCenterAdvertiserListV2Api() *CustomerCenterAdvertiserListV2ApiService {
	return c.ApiClient.CustomerCenterAdvertiserListV2ApiService
}

func (c *Client) AccountFundGetV3Api() *AccountFundGetV3ApiService {
	return c.ApiClient.AccountFundGetV3ApiService
}

func (c *Client) FileImageAdV2Api() *FileImageAdV2ApiService {
	return c.ApiClient.FileImageAdV2ApiService
}
func (c *Client) FileUploadTaskCreateV2Api() *FileUploadTaskCreateV2ApiService {
	return c.ApiClient.FileUploadTaskCreateV2ApiService
}

func (c *Client) BusinessPlatformCompanyInfoGetV3Api() *BusinessPlatformCompanyInfoGetV3ApiService {
	return c.ApiClient.BusinessPlatformCompanyInfoGetV3ApiService
}

func (c *Client) AdvertiserBudgetGetV2Api() *AdvertiserBudgetGetV2ApiService {
	return c.ApiClient.AdvertiserBudgetGetV2ApiService
}

func (c *Client) ProjectListV3Api() *ProjectListV3ApiService {
	return c.ApiClient.ProjectListV3ApiService
}

func (c *Client) PromotionListV3Api() *PromotionListV3ApiService {
	return c.ApiClient.PromotionListV3ApiService
}

func (c *Client) ProjectStatusUpdateV3Api() *ProjectStatusUpdateV3ApiService {
	return c.ApiClient.ProjectStatusUpdateV3ApiService
}

func (c *Client) PromotionStatusUpdateV3Api() *PromotionStatusUpdateV3ApiService {
	return c.ApiClient.PromotionStatusUpdateV3ApiService
}

func (c *Client) AdvertiserBudgetUpdateV2Api() *AdvertiserBudgetUpdateV2ApiService {
	return c.ApiClient.AdvertiserBudgetUpdateV2ApiService
}

func (c *Client) ProjectBudgetUpdateV3Api() *ProjectBudgetUpdateV3ApiService {
	return c.ApiClient.ProjectBudgetUpdateV3ApiService
}

func (c *Client) ProjectCreateV3Api() *ProjectCreateV3ApiService {
	return c.ApiClient.ProjectCreateV3ApiService
}

func (c *Client) PromotionBudgetUpdateV3Api() *PromotionBudgetUpdateV3ApiService {
	return c.ApiClient.PromotionBudgetUpdateV3ApiService
}

func (c *Client) PromotionBidUpdateV3Api() *PromotionBidUpdateV3ApiService {
	return c.ApiClient.PromotionBidUpdateV3ApiService
}

func (c *Client) ToolsWechatAppletCreateV3Api() *ToolsWechatAppletCreateV3ApiService {
	return c.ApiClient.ToolsWechatAppletCreateV3ApiService
}

func (c *Client) ToolsWechatAppletListV3Api() *ToolsWechatAppletListV3ApiService {
	return c.ApiClient.ToolsWechatAppletListV3ApiService
}

func (c *Client) ToolsMicroAppCreateV3Api() *ToolsMicroAppCreateV3ApiService {
	return c.ApiClient.ToolsMicroAppCreateV3ApiService
}

func (c *Client) ToolsMicroAppListV3Api() *ToolsMicroAppListV3ApiService {
	return c.ApiClient.ToolsMicroAppListV3ApiService
}

func (c *Client) ToolsMicroAppUpdateV3Api() *ToolsMicroAppUpdateV3ApiService {
	return c.ApiClient.ToolsMicroAppUpdateV3ApiService
}

func (c *Client) ToolsAssetLinkListV3Api() *ToolsAssetLinkListV3ApiService {
	return c.ApiClient.ToolsAssetLinkListV3ApiService
}

func (c *Client) DmpCustomAudienceSelectV2Api() *DmpCustomAudienceSelectV2ApiService {
	return c.ApiClient.DmpCustomAudienceSelectV2ApiService
}

func (c *Client) DmpCustomAudienceReadV2Api() *DmpCustomAudienceReadV2ApiService {
	return c.ApiClient.DmpCustomAudienceReadV2ApiService
}

func (c *Client) DmpCustomAudiencePublishV2Api() *DmpCustomAudiencePublishV2ApiService {
	return c.ApiClient.DmpCustomAudiencePublishV2ApiService
}

func (c *Client) DmpCustomAudiencePushV2Api() *DmpCustomAudiencePushV2ApiService {
	return c.ApiClient.DmpCustomAudiencePushV2ApiService
}

func (c *Client) DmpCustomAudienceDeleteV2Api() *DmpCustomAudienceDeleteV2ApiService {
	return c.ApiClient.DmpCustomAudienceDeleteV2ApiService
}

func (c *Client) ToolsEventAllAssetsListGetV2Api() *ToolsEventAllAssetsListGetV2ApiService {
	return c.ApiClient.ToolsEventAllAssetsListGetV2ApiService
}

func (c *Client) ToolsEventAllAssetsDetailGetV2Api() *ToolsEventAllAssetsDetailGetV2ApiService {
	return c.ApiClient.ToolsEventAllAssetsDetailGetV2ApiService
}

func (c *Client) EventManagerEventConfigsGetV2Api() *EventManagerEventConfigsGetV2ApiService {
	return c.ApiClient.EventManagerEventConfigsGetV2ApiService
}

func (c *Client) AudiencePackageCreateV2Api() *AudiencePackageCreateV2ApiService {
	return c.ApiClient.AudiencePackageCreateV2ApiService
}

func (c *Client) AudiencePackageDeleteV2Api() *AudiencePackageDeleteV2ApiService {
	return c.ApiClient.AudiencePackageDeleteV2ApiService
}

func (c *Client) AudiencePackageGetV3Api() *AudiencePackageGetV3ApiService {
	return c.ApiClient.AudiencePackageGetV3ApiService
}

func (c *Client) ToolsEstimateAudienceV2Api() *ToolsEstimateAudienceV2ApiService {
	return c.ApiClient.ToolsEstimateAudienceV2ApiService
}

func (c *Client) ToolsAdminInfoGetV2Api() *ToolsAdminInfoGetV2ApiService {
	return c.ApiClient.ToolsAdminInfoGetV2ApiService
}

func (c *Client) ToolsInterestActionActionCategoryV2Api() *ToolsInterestActionActionCategoryV2ApiService {
	return c.ApiClient.ToolsInterestActionActionCategoryV2ApiService
}

func (c *Client) ToolsInterestActionInterestCategoryV2Api() *ToolsInterestActionInterestCategoryV2ApiService {
	return c.ApiClient.ToolsInterestActionInterestCategoryV2ApiService
}

func (c *Client) ToolsInterestActionActionKeywordV2Api() *ToolsInterestActionActionKeywordV2ApiService {
	return c.ApiClient.ToolsInterestActionActionKeywordV2ApiService
}

func (c *Client) ToolsInterestActionKeywordSuggestV2Api() *ToolsInterestActionKeywordSuggestV2ApiService {
	return c.ApiClient.ToolsInterestActionKeywordSuggestV2ApiService
}

func (c *Client) ToolsInterestActionInterestKeywordV2Api() *ToolsInterestActionInterestKeywordV2ApiService {
	return c.ApiClient.ToolsInterestActionInterestKeywordV2ApiService
}

func (c *Client) ToolsInterestActionActionId2wordV2Api() *ToolsInterestActionActionId2wordV2ApiService {
	return c.ApiClient.ToolsInterestActionActionId2wordV2ApiService
}

func (c *Client) ToolsAwemeMultiLevelCategoryGetV2Api() *ToolsAwemeMultiLevelCategoryGetV2ApiService {
	return c.ApiClient.ToolsAwemeMultiLevelCategoryGetV2ApiService
}

func (c *Client) ToolsAwemeCategoryTopAuthorGetV2Api() *ToolsAwemeCategoryTopAuthorGetV2ApiService {
	return c.ApiClient.ToolsAwemeCategoryTopAuthorGetV2ApiService
}

func (c *Client) ToolsAwemeInfoSearchGetV2Api() *ToolsAwemeInfoSearchGetV2ApiService {
	return c.ApiClient.ToolsAwemeInfoSearchGetV2ApiService
}

func (c *Client) ToolsAwemeSimilarAuthorSearchGetV2Api() *ToolsAwemeSimilarAuthorSearchGetV2ApiService {
	return c.ApiClient.ToolsAwemeSimilarAuthorSearchGetV2ApiService
}

func (c *Client) ToolsQuotaGetV2Api() *ToolsQuotaGetV2ApiService {
	return c.ApiClient.ToolsQuotaGetV2ApiService
}

func (c *Client) ToolsOrangeSiteGetV3Api() *ToolsOrangeSiteGetV3ApiService {
	return c.ApiClient.ToolsOrangeSiteGetV3ApiService
}

func (c *Client) DpaProductAvailablesV2Api() *DpaProductAvailablesV2ApiService {
	return c.ApiClient.DpaProductAvailablesV2ApiService
}

func (c *Client) DpaDetailGetV2Api() *DpaDetailGetV2ApiService {
	return c.ApiClient.DpaDetailGetV2ApiService
}

func (c *Client) DpaProductDetailGetV2Api() *DpaProductDetailGetV2ApiService {
	return c.ApiClient.DpaProductDetailGetV2ApiService
}

func (c *Client) DpaClueProductDetailV2Api() *DpaClueProductDetailV2ApiService {
	return c.ApiClient.DpaClueProductDetailV2ApiService
}
