package api

import (
	"context"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/gogf/gf/v2/frame/g"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"net/http"
	"strings"
	"sync"
	"time"
)

type AdKSService struct {
	AppId  int64  `json:"appId"`
	Secret string `json:"secret"`
}

var ()

var (
	instance    *AdKSIClient
	once        sync.Once
	ksAdService *AdKSService
)

func init() {
	ksAdService = new(AdKSService)
	g.Cfg().MustGet(context.Background(), "advertiser.ks").Scan(&ksAdService)
}

type Configuration struct {
	BasePath   string `json:"basePath,omitempty"`
	HTTPClient *resty.Client
}

func NewConfiguration() *Configuration {
	cfg := &Configuration{
		BasePath:   "https://ad.e.kuaishou.com",
		HTTPClient: resty.New(),
	}
	return cfg
}

func NewAPIClient(cfg *Configuration) *AdKSIClient {
	if cfg.HTTPClient == nil {
		cfg.HTTPClient = resty.New()
	}
	c := &AdKSIClient{}
	c.Cfg = cfg
	c.GetTokenService = new(GetTokenService)
	c.GetTokenService.SetCfg(c.Cfg)

	c.GetAdListService = new(GetAdListService)
	c.GetTokenService.SetCfg(c.Cfg)

	//*GetReTokenService
	c.GetReTokenService = new(GetReTokenService)
	c.GetReTokenService.SetCfg(c.Cfg)
	//*QueryCoreDataService
	c.QueryCoreDataService = new(QueryCoreDataService)
	c.QueryCoreDataService.SetCfg(c.Cfg)
	//*QueryAdDataService
	c.QueryAdDataService = new(QueryAdDataService)
	c.QueryAdDataService.SetCfg(c.Cfg)
	//*QuerySalerCopyRightDataService
	c.QuerySalerCopyRightDataService = new(QuerySalerCopyRightDataService)
	c.QuerySalerCopyRightDataService.SetCfg(c.Cfg)
	//*QuerySettleDataService
	c.QuerySettleDataService = new(QuerySettleDataService)
	c.QuerySettleDataService.SetCfg(c.Cfg)
	//*QueryAccountInfoService
	c.QueryAccountInfoService = new(QueryAccountInfoService)
	c.QueryAccountInfoService.SetCfg(c.Cfg)
	//QueryAdDetailService
	c.QueryAdDetailService = new(QueryAdDetailService)
	c.QueryAdDetailService.SetCfg(c.Cfg)
	// QuerySeriesInfoService
	c.QuerySeriesInfoService = new(QuerySeriesInfoService)
	c.QuerySeriesInfoService.SetCfg(c.Cfg)
	// QueryOrderDetailService
	c.QueryOrderDetailService = new(QueryOrderDetailService)
	c.QueryOrderDetailService.SetCfg(c.Cfg)

	//GetAdvertiserInfoService
	c.GetAdvertiserInfoService = new(GetAdvertiserInfoService)
	c.GetAdvertiserInfoService.SetCfg(c.Cfg)
	//QueryAdvertiserFundService
	c.QueryAdvertiserFundService = new(QueryAdvertiserFundService)
	c.QueryAdvertiserFundService.SetCfg(c.Cfg)
	// GetFetchAccountListService
	c.GetFetchAccountListService = new(GetFetchAccountListService)
	c.GetFetchAccountListService.SetCfg(c.Cfg)
	//FileAdVideoGetService
	c.FileAdVideoGetService = new(FileAdVideoGetService)
	c.FileAdVideoGetService.SetCfg(c.Cfg)

	//GetDspCampaignListService
	c.GetDspCampaignListService = new(GetDspCampaignListService)
	c.GetDspCampaignListService.SetCfg(c.Cfg)
	//GetDspUnitListService
	c.GetDspUnitListService = new(GetDspUnitListService)
	c.GetDspUnitListService.SetCfg(c.Cfg)

	c.AccountReportService = new(AccountReportService)
	c.AccountReportService.SetCfg(c.Cfg)
	c.CampaignReportService = new(CampaignReportService)
	c.CampaignReportService.SetCfg(c.Cfg)
	c.UnitReportService = new(UnitReportService)
	c.UnitReportService.SetCfg(c.Cfg)
	//CreativeReviewDetailsService
	c.CreativeReviewDetailsService = new(CreativeReviewDetailsService)
	c.CreativeReviewDetailsService.SetCfg(c.Cfg)

	// 账户层级
	c.OcpxTypesService = new(OcpxTypesService)
	c.OcpxTypesService.SetCfg(c.Cfg)
	c.ModAccountAutoInfoService = new(ModAccountAutoInfoService)
	c.ModAccountAutoInfoService.SetCfg(c.Cfg)
	c.QueryAccountAutoInfoService = new(QueryAccountAutoInfoService)
	c.QueryAccountAutoInfoService.SetCfg(c.Cfg)
	c.QueryAccountBudgetService = new(QueryAccountBudgetService)
	c.QueryAccountBudgetService.SetCfg(c.Cfg)
	c.UpdateAccountBudgetService = new(UpdateAccountBudgetService)
	c.UpdateAccountBudgetService.SetCfg(c.Cfg)

	// ProgramCreativeReportService
	c.ProgramCreativeReportService = new(ProgramCreativeReportService)
	c.ProgramCreativeReportService.SetCfg(c.Cfg)

	// 增量探索
	c.QueryAccountIncExploreOcpxTypesService = new(QueryAccountIncExploreOcpxTypesService)
	c.QueryAccountIncExploreOcpxTypesService.SetCfg(c.Cfg)
	c.AddAccountIncExploreService = new(AddAccountIncExploreService)
	c.AddAccountIncExploreService.SetCfg(c.Cfg)
	c.QueryAccountIncExploreService = new(QueryAccountIncExploreService)
	c.QueryAccountIncExploreService.SetCfg(c.Cfg)
	c.UpdateAccountIncExploreService = new(UpdateAccountIncExploreService)
	c.UpdateAccountIncExploreService.SetCfg(c.Cfg)
	c.DeleteAccountIncExploreService = new(DeleteAccountIncExploreService)
	c.DeleteAccountIncExploreService.SetCfg(c.Cfg)
	c.PauseAccountIncExploreService = new(PauseAccountIncExploreService)
	c.PauseAccountIncExploreService.SetCfg(c.Cfg)
	c.RebootAccountIncExploreService = new(RebootAccountIncExploreService)
	c.RebootAccountIncExploreService.SetCfg(c.Cfg)
	//AdImageUploadService
	c.AdImageUploadService = new(AdImageUploadService)
	c.AdImageUploadService.SetCfg(c.Cfg)
	// UploadTokenGenerateService
	c.UploadTokenGenerateService = new(UploadTokenGenerateService)
	c.UploadTokenGenerateService.SetCfg(c.Cfg)
	// PostAdVideoUploadService  分片上传
	c.PostAdVideoUploadService = new(PostAdVideoUploadService)
	c.PostAdVideoUploadService.SetCfg(c.Cfg)
	// FileAdVideoUploadService
	c.FileAdVideoUploadService = new(FileAdVideoUploadService)
	c.FileAdVideoUploadService.SetCfg(c.Cfg)

	// 广告计划
	c.UpdateCampaignStatusService = new(UpdateCampaignStatusService)
	c.UpdateCampaignStatusService.SetCfg(c.Cfg)
	c.UpdateCampaignService = new(UpdateCampaignService)
	c.UpdateCampaignService.SetCfg(c.Cfg)

	// 广告组
	c.UpdateUnitStatusService = new(UpdateUnitStatusService)
	c.UpdateUnitStatusService.SetCfg(c.Cfg)
	c.UpdateUnitService = new(UpdateUnitService)
	c.UpdateUnitService.SetCfg(c.Cfg)

	// 广告搭建
	//AdvancedCreativeCreateService
	c.AdvancedCreativeCreateService = new(AdvancedCreativeCreateService)
	c.AdvancedCreativeCreateService.SetCfg(c.Cfg)
	//CreateCreativeService
	c.CreateCreativeService = new(CreateCreativeService)
	c.CreateCreativeService.SetCfg(c.Cfg)
	//CreateCampaignService
	c.CreateCampaignService = new(CreateCampaignService)
	c.CreateCampaignService.SetCfg(c.Cfg)
	//CreateUnitService
	c.CreateUnitService = new(CreateUnitService)
	c.CreateUnitService.SetCfg(c.Cfg)

	c.QuerySeriesAuthUserListService = new(QuerySeriesAuthUserListService)
	c.QuerySeriesAuthUserListService.SetCfg(c.Cfg)
	c.QuerySeriesListService = new(QuerySeriesListService)
	c.QuerySeriesListService.SetCfg(c.Cfg)
	c.QuerySeriesEpisodeListService = new(QuerySeriesEpisodeListService)
	c.QuerySeriesEpisodeListService.SetCfg(c.Cfg)
	c.QuerySeriesPayModeTypeService = new(QuerySeriesPayModeTypeService)
	c.QuerySeriesPayModeTypeService.SetCfg(c.Cfg)
	c.QuerySeriesPayModeTemplateService = new(QuerySeriesPayModeTemplateService)
	c.QuerySeriesPayModeTemplateService.SetCfg(c.Cfg)
	c.QueryProductService = new(QueryProductService)
	c.QueryProductService.SetCfg(c.Cfg)
	c.QueryProductLibraryListService = new(QueryProductLibraryListService)
	c.QueryProductLibraryListService.SetCfg(c.Cfg)
	c.QueryCreativeActionBarTextService = new(QueryCreativeActionBarTextService)
	c.QueryCreativeActionBarTextService.SetCfg(c.Cfg)
	c.QueryToolExposeTagsService = new(QueryToolExposeTagsService)
	c.QueryToolExposeTagsService.SetCfg(c.Cfg)
	c.QueryCreativeCategoryListService = new(QueryCreativeCategoryListService)
	c.QueryCreativeCategoryListService.SetCfg(c.Cfg)

	return c
}

// GetKSApiClient 获取快手的API客户端
func GetKSApiClient() *AdKSIClient {
	once.Do(func() {
		configuration := NewConfiguration()
		client := resty.New()
		client.SetTimeout(10 * time.Second) //设置全局超时时间
		client.SetTransport(&http.Transport{
			MaxIdleConnsPerHost:   10,               // 对于每个主机，保持最大空闲连接数为 10
			IdleConnTimeout:       30 * time.Second, // 空闲连接超时时间为 30 秒
			TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间为 10 秒
			ResponseHeaderTimeout: 20 * time.Second, // 等待响应头的超时时间为 20 秒
		})
		configuration.HTTPClient = client
		instance = NewAPIClient(configuration)
	})
	return instance
}

// KsBaseResp 泛型结构体
type KsBaseResp[T any] struct {
	Code     int    `json:"code"`
	Message  string `json:"message"`
	Data     *T     `json:"data"`
	ErrorMsg string `json:"error_msg"` // 具体报错信息
}

const (
	KsRefreshToken      = "KS:REFRESH:TOKEN:"
	KsAccessToken       = "KS:ACCESS:TOKEN:"
	KsAdvertiserList    = "KS:ADVERTISER:List:"
	KsPullLastAccountId = "KSPULLLASTACCOUNTID:"
)

// GetLastAccountIdKey
func GetLastAccountIdKey(agentId, UserId int64) string {
	return fmt.Sprintf("%s%v:%v", KsPullLastAccountId, agentId, UserId)
}

func GetReTokenKey(aId int64) string {
	return fmt.Sprintf("%s%v", KsRefreshToken, aId)
}

func GetTokenKey(aId int64) string {
	return fmt.Sprintf("%s%v", KsAccessToken, aId)
}

func GetAccessTokenByAppIdCache(aId int64) (token string) {
	//查询广告主所属商务账号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	token = commonService.GetGoRedis().Get(ctx, GetTokenKey(aId)).Val()
	if len(token) == 0 {
		// 刷新token 并更新缓存
		reToken := commonService.GetGoRedis().Get(ctx, GetReTokenKey(aId)).Val()
		reToken = strings.ReplaceAll(reToken, `"`, "")
		reTokenRes, err := GetKSApiClient().GetReTokenService.SetReq(GetReTokenReq{
			AppId:        ksAdService.AppId,
			Secret:       ksAdService.Secret,
			RefreshToken: reToken,
		}).Do()
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
		if reTokenRes.Code != 0 {
			err = fmt.Errorf("GetKSAccessTokenByAppIdCache失败: %s", reTokenRes.Message)
			return
		}
		token = reTokenRes.Data.AccessToken
		// 设置token
		var accessToken = reTokenRes.Data.AccessToken
		var expiresIn = reTokenRes.Data.AccessTokenExpiresIn - 60
		var refreshToken = reTokenRes.Data.RefreshToken
		var refreshTokenExpiresIn = reTokenRes.Data.RefreshTokenExpiresIn
		// 给渠道获取消耗的appId
		commonService.GetGoRedis().Set(ctx, GetReTokenKey(aId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, GetTokenKey(aId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
	}
	token = strings.ReplaceAll(token, `"`, "")
	return
}

func GetAgentReTokenKey(agentId, userId int64) string {
	return fmt.Sprintf("%s%v:%v", KsRefreshToken, agentId, userId)
}

func GetAgentTokenKey(agentId, userId int64) string {
	return fmt.Sprintf("%s%v:%v", KsAccessToken, agentId, userId)
}

// GetAccessTokenByAgentCache agent 代理商获取token
func GetAccessTokenByAgentCache(agentId, userId, appId int64, secret string) (token string) {
	//查询广告主所属商务账号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	token = commonService.GetGoRedis().Get(ctx, GetAgentTokenKey(agentId, userId)).Val()

	// 根据agentId 和UserId 获取一个appid
	//agentInfo, err := service.KsAdvertiserAgentInfo().GetByAgentIdAndKsUserId(ctx, agentId, userId)
	//if err != nil || agentInfo == nil {
	//	g.Log().Errorf(ctx, "获取token失败err:%v", err.Error())
	//}
	//
	//appConfig, _ := service.AdAppConfig().GetByAppId(ctx, gconv.String(agentInfo.AppId))
	//if appConfig == nil {
	//	return ""
	//}
	if len(token) == 0 {
		// 刷新token 并更新缓存
		reToken := commonService.GetGoRedis().Get(ctx, GetAgentReTokenKey(agentId, userId)).Val()
		reToken = strings.ReplaceAll(reToken, `"`, "")
		reTokenRes, err := GetKSApiClient().GetReTokenService.SetReq(GetReTokenReq{
			AppId:        appId,
			Secret:       secret,
			RefreshToken: reToken,
		}).Do()
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
		if reTokenRes.Code != 0 {
			err = fmt.Errorf("GetKSAccessTokenByAppIdCache失败: %s", reTokenRes.Message)
			return
		}
		token = reTokenRes.Data.AccessToken
		// 设置token
		var accessToken = reTokenRes.Data.AccessToken
		var expiresIn = reTokenRes.Data.AccessTokenExpiresIn - 60
		var refreshToken = reTokenRes.Data.RefreshToken
		var refreshTokenExpiresIn = reTokenRes.Data.RefreshTokenExpiresIn
		// 给渠道获取消耗的appId
		commonService.GetGoRedis().Set(ctx, GetAgentReTokenKey(agentId, userId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, GetAgentTokenKey(agentId, userId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
	}
	token = strings.ReplaceAll(token, `"`, "")
	return
}

func GetAdvertiserReTokenKey(advertiserId, userId int64) string {
	return fmt.Sprintf("%s%v:%v", KsRefreshToken, advertiserId, userId)
}

func GetAdvertiserTokenKey(advertiserId, userId int64) string {
	return fmt.Sprintf("%s%v:%v", KsAccessToken, advertiserId, userId)
}

func GetAccessTokenByAdvertiserCache(advertiserId, userId int64) (token string) {
	//查询广告主所属商务账号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	token = commonService.GetGoRedis().Get(ctx, GetAdvertiserTokenKey(advertiserId, userId)).Val()
	if len(token) == 0 {
		// 刷新token 并更新缓存
		reToken := commonService.GetGoRedis().Get(ctx, GetAdvertiserReTokenKey(advertiserId, userId)).Val()
		reToken = strings.ReplaceAll(reToken, `"`, "")
		reTokenRes, err := GetKSApiClient().GetReTokenService.SetReq(GetReTokenReq{
			AppId:        ksAdService.AppId,
			Secret:       ksAdService.Secret,
			RefreshToken: reToken,
		}).Do()
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
		if reTokenRes.Code != 0 {
			err = fmt.Errorf("GetKSAccessTokenByAppIdCache失败: %s", reTokenRes.Message)
			return
		}
		token = reTokenRes.Data.AccessToken
		// 设置token
		var accessToken = reTokenRes.Data.AccessToken
		var expiresIn = reTokenRes.Data.AccessTokenExpiresIn - 60
		var refreshToken = reTokenRes.Data.RefreshToken
		var refreshTokenExpiresIn = reTokenRes.Data.RefreshTokenExpiresIn
		// 给渠道获取消耗的appId
		commonService.GetGoRedis().Set(ctx, GetAdvertiserReTokenKey(advertiserId, userId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, GetAdvertiserTokenKey(advertiserId, userId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
	}
	token = strings.ReplaceAll(token, `"`, "")
	return
}
